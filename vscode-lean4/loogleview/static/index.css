.loader {
    display: inline-block;
    margin-left: 8px;
    border: 4px solid #f3f3f3; /* Light grey */
    border-top: 4px solid #3498db; /* Blue */
    border-radius: 50%;
    width: 16px;
    height: 16px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.hidden {
    display: none;
}

.query-label {
    display: block;
    color: var(--vscode-foreground);
    cursor: pointer;
    font-size: var(--vscode-font-size);
    line-height: normal;
    margin-bottom: 2px;
}

.query-container {
    display: flex;
    align-items: center;
}

.input-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 70ch;

    box-sizing: border-box;
    position: relative;
    flex-direction: row;
    color: var(--vscode-input-foreground);
    background: var(--vscode-input-background);
    border-radius: 2px;
    border: 1px solid var(--vscode-dropdown-border);
    height: 26px;
    min-width: 100px;
    font-family: var(--vscode-font-family);
    outline: none;
    user-select: none;
}

.input-container:focus-within:not([disabled]) {
    border-color: var(--vscode-focusBorder);
}

[contenteditable='true'].single-line {
    white-space: nowrap;
    width: 60ch;
    overflow: hidden;
    outline: none;

    height: calc(100% - 4px);
    margin-top: auto;
    margin-bottom: auto;
    border: none;
    padding: 0 9px;
    font-size: var(--vscode-font-size);
    line-height: normal;
}
[contenteditable='true'].single-line br {
    display: none;
}
[contenteditable='true'].single-line * {
    display: inline;
    white-space: nowrap;
}

vscode-button {
    vertical-align: middle;
}

.input-buttons {
    display: inline-flex;
    margin-left: 0.5em;
    margin-right: 0.5em;
}

#suggestions a {
    text-decoration: var(--text-link-decoration);
}
