# Lean 4 VS Code Extension
This extension provides VS Code support for the Lean 4 theorem prover and programming language.

## Installing Lean 4

After installing this extension, a 'Welcome' page with a setup guide should open automatically. The setup guide provides platform-specific information on the following topics:
- Books and documentation resources
- Installing dependencies that are needed by Lean
- Installing Lean's version manager <PERSON><PERSON> and a recent stable version of Lean 4
- Setting up a Lean 4 project
- Troubleshooting issues

If the setup guide does not open automatically, you can still open it manually by opening an empty file, clicking on the ∀-symbol in the top right and selecting 'Documentation…' > 'Show Setup Guide'.

  ![Setup guide with instructions for how to re-open the setup guide manually](https://github.com/leanprover/vscode-lean4/raw/HEAD/vscode-lean4/images/setup_guide.png)

## Using this extension

The [Lean 4 VS Code extension manual](https://github.com/leanprover/vscode-lean4/blob/master/vscode-lean4/manual/manual.md) provides a complete and detailed overview over all features provided by this VS Code extension. If you are new to <PERSON>n, you may find the first five subsections of the 'Interacting with Lean files' section in the manual to be very helpful.

  ![Manual table of contents](https://github.com/leanprover/vscode-lean4/raw/HEAD/vscode-lean4/images/manual.png)
