{"name": "lean4", "displayName": "Lean 4", "description": "Lean 4 language support for VS Code", "version": "0.0.209", "publisher": "leanprover", "engines": {"vscode": "^1.75.0"}, "categories": ["Programming Languages"], "keywords": ["<PERSON>n", "Lean 4", "lean4", "<PERSON><PERSON>", "InfoView"], "repository": {"type": "git", "url": "https://github.com/leanprover/vscode-lean4.git"}, "homepage": "https://lean-lang.org", "contributes": {"configuration": {"type": "object", "title": "Lean 4", "properties": {"lean4.input.enabled": {"type": "boolean", "default": true, "markdownDescription": "Enable Lean input mode."}, "lean4.input.customTranslations": {"type": "object", "items": {"type": "string", "description": "Unicode character to translate to"}, "default": {}, "markdownDescription": "Add additional input Unicode translations. Example: `{\"foo\": \"☺\"}` will correct `\\foo` to `☺`."}, "lean4.input.languages": {"type": "array", "default": ["lean4"], "markdownDescription": "Enable Lean Unicode input in other file types.", "items": {"type": "string", "description": "the name of a language, e.g. 'lean4', 'markdown'"}}, "lean4.input.leader": {"type": "string", "default": "\\", "markdownDescription": "Leader key to trigger input mode."}, "lean4.input.eagerReplacementEnabled": {"type": "boolean", "default": true, "markdownDescription": "Enable eager replacement of abbreviations that uniquely identify a symbol."}, "lean4.fallBackToStringOccurrenceHighlighting": {"type": "boolean", "description": "Fall back to string-based occurrence highlighting when there are no semantic symbol occurrences from the Lean language server to highlight.", "default": false}, "lean4.automaticallyBuildDependencies": {"type": "boolean", "default": false, "markdownDescription": "Enable automatically building dependencies when opening a file. In Lean versions pre-4.2.0, dependencies are always built automatically regardless of this setting."}, "lean4.envPathExtensions": {"type": "array", "default": [], "markdownDescription": "Additional entries to add to the PATH variable of the Lean 4 VS Code extension process and any of its child processes.", "items": {"type": "string", "description": "Entry to add to the PATH variable"}}, "lean4.alwaysAskBeforeInstallingLeanVersions": {"type": "boolean", "default": false, "markdownDescription": "Enable to display a confirmation prompt whenever <PERSON><PERSON> is about to download and install a new Lean version. Needs Elan >= 4.0.0."}, "lean4.serverArgs": {"type": "array", "default": [], "description": "Arguments to pass to the Lean 4 server.", "items": {"type": "string", "description": "an argument to pass to the server"}}, "lean4.trace.server": {"type": "string", "default": "off", "enum": ["off", "messages", "compact", "verbose"], "markdownDescription": "Whether to enable client-side logging for language server messages."}, "lean4.serverLogging.enabled": {"type": "boolean", "default": false, "description": "Enable Lean 4 server logging."}, "lean4.serverLogging.path": {"type": "string", "default": ".", "description": "Path to the directory where Lean 4 server log files are stored.", "scope": "machine-overridable"}, "lean4.autofocusOutput": {"type": "boolean", "default": false, "description": "Automatically show the Output panel when the Lean 4 server prints a new message."}, "lean4.infoview.autoOpen": {"type": "boolean", "default": true, "markdownDescription": "Open infoview when Lean extension is activated for the first time."}, "lean4.infoview.autoOpenShowsGoal": {"type": "boolean", "default": true, "markdownDescription": "When auto-opened, the infoview shows goal and messages for the current line (instead of all messages for the whole file)."}, "lean4.infoview.allErrorsOnLine": {"type": "boolean", "default": true, "markdownDescription": "Show all errors on the current line, instead of just the ones to the right of the cursor."}, "lean4.infoview.debounceTime": {"type": "number", "default": 50, "markdownDescription": "How long (in milliseconds) the infoview waits before displaying new information after the cursor has stopped moving. The optimal value depends on your machine - try experimenting."}, "lean4.infoview.style": {"type": "string", "default": "", "markdownDescription": "Add an additional CSS snippet to the infoview."}, "lean4.infoview.showExpectedType": {"type": "boolean", "default": true, "markdownDescription": "Show the expected type by default.", "deprecationMessage": "Superseded by the 'InfoView: Expected Type Visibility' setting."}, "lean4.infoview.expectedTypeVisibility": {"type": "string", "enum": ["Expanded by default", "Collapsed by default", "Hidden"], "default": "Expanded by default", "markdownDescription": "Default visibility of the expected type."}, "lean4.infoview.showGoalNames": {"type": "boolean", "default": true, "markdownDescription": "Show goal names (e.g. `case inl`) in the infoview."}, "lean4.infoview.emphasizeFirstGoal": {"type": "boolean", "default": false, "markdownDescription": "Display goals other than the main goal in a smaller font size."}, "lean4.infoview.reverseTacticState": {"title": "Display Target Before Assumptions", "type": "boolean", "default": false, "markdownDescription": "Default to showing the goal type first and then the local context."}, "lean4.infoview.hideTypeAssumptions": {"type": "boolean", "default": false, "markdownDescription": "Default to hiding type assumptions from the local context."}, "lean4.infoview.hideInstanceAssumptions": {"type": "boolean", "default": false, "markdownDescription": "Default to filtering instance assumptions from the local context."}, "lean4.infoview.hideInaccessibleAssumptions": {"type": "boolean", "default": false, "markdownDescription": "Default to filtering hidden assumptions from the local context."}, "lean4.infoview.hideLetValues": {"type": "boolean", "default": false, "markdownDescription": "Default to filtering type assumptions from the local context."}, "lean4.infoview.showTooltipOnHover": {"type": "boolean", "default": true, "markdownDescription": "Show the tooltip for an interactive element (e.g. a subexpression) when the pointer hovers over that element. When disabled, the element must be clicked for the tooltip to show up."}, "lean4.elaborationDelay": {"type": "number", "default": 200, "description": "Time (in milliseconds) which must pass since latest edit until elaboration begins. Lower values may make editing feel faster at the cost of higher CPU usage."}, "lean4.showSetupWarnings": {"type": "boolean", "default": true, "markdownDescription": "Show warning notifications when the Lean setup has warning-level issues."}, "lean4.alwaysShowTitleBarMenu": {"type": "boolean", "default": true, "markdownDescription": "Always display the Lean extension title bar menu in the top right. This helps beginners create and open Lean projects after launching an empty instance of VS Code, but may not be desirable for anyone who uses VS Code for things other than Lean."}, "lean4.showDiagnosticGutterDecorations": {"type": "boolean", "default": true, "markdownDescription": "Display icons for errors and warnings and error ranges in the gutter."}, "lean4.showUnsolvedGoalsDecoration": {"type": "boolean", "default": true, "markdownDescription": "Display an icon at the end of the line where an 'unsolved goals' error ends."}, "lean4.unsolvedGoalsDecorationLightThemeColor": {"type": "string", "default": "editorInfo.foreground", "markdownDescription": "Color used in unsolved goal decorations when using a light theme. Can either denote a CSS color or a [VS Code theme color](https://code.visualstudio.com/api/references/theme-color)."}, "lean4.unsolvedGoalsDecorationDarkThemeColor": {"type": "string", "default": "editorInfo.foreground", "markdownDescription": "Color used in unsolved goal decorations when using a dark theme. Can either denote a CSS color or a [VS Code theme color](https://code.visualstudio.com/api/references/theme-color)."}, "lean4.goalsAccomplishedDecorationKind": {"type": "string", "enum": ["Off", "Double Checkmark", "Circled Checkmark", "Octopus", "<PERSON><PERSON>"], "default": "Double Checkmark", "markdownDescription": "Style of icon used in 'Goals accomplished' decoration."}, "lean4.decorationEditDelay": {"type": "number", "default": 750, "markdownDescription": "Delay after an edit in milliseconds until certain editor decorations (like the 'unsolved goals' decoration) update."}}}, "commands": [{"command": "lean4.restartServer", "category": "Lean 4: <PERSON>", "title": "Restart Server", "description": "Restarts the Lean server (for all files)."}, {"command": "lean4.stopServer", "category": "Lean 4: <PERSON>", "title": "Stop Server", "description": "Stops the Lean server (for all files)."}, {"command": "lean4.restartFile", "category": "Lean 4: <PERSON>", "title": "Restart File", "description": "Restarts the Lean server for the file that is currently focused, refreshing the dependencies."}, {"command": "lean4.refreshFileDependencies", "category": "Lean 4: <PERSON>", "title": "Refresh File Dependencies", "description": "Restarts the Lean server for the file that is currently focused to refresh the dependencies."}, {"command": "lean4.redisplaySetupError", "category": "Lean 4", "title": "Re-Display Active Setup Error", "description": "Re-displays the currently active setup error if it was closed previously."}, {"command": "lean4.input.convert", "category": "Lean 4: Input", "title": "Convert Current Abbreviation", "description": "Converts the current abbreviation (e.g. \\lam)."}, {"command": "lean4.displayGoal", "category": "Lean 4: InfoView", "title": "Display Goal", "description": "Display the goal and any messages at the current position in the infoview.", "icon": {"dark": "./media/display-goal-dark.svg", "light": "./media/display-goal-light.svg"}}, {"command": "lean4.toggleInfoview", "category": "Lean 4: InfoView", "title": "Toggle InfoView", "description": "Toggle whether the infoview is displayed."}, {"command": "lean4.displayList", "category": "Lean 4: InfoView", "title": "Toggle \"All Messages\"", "description": "Toggles the \"All Messages\" panel in the infoview."}, {"command": "lean4.infoView.copyToComment", "category": "Lean 4: InfoView", "title": "Copy Contents to Comment", "description": "Copy the contents of the currently active tactic state panel to a new comment on the previous line.", "icon": {"dark": "./media/copy-to-comment-dark.svg", "light": "./media/copy-to-comment-light.svg"}}, {"command": "lean4.infoView.toggleStickyPosition", "category": "Lean 4: InfoView", "title": "Toggle <PERSON>n", "description": "Create or remove a tactic state panel pinned to show the goal at the current position."}, {"command": "lean4.infoView.toggleUpdating", "category": "Lean 4: InfoView", "title": "Toggle Updating", "description": "Pause / Continue live updating of the main tactic state widget."}, {"command": "lean4.infoView.toggleExpectedType", "category": "Lean 4: InfoView", "title": "Toggle Expected Type", "description": "Toggle the display of expected type."}, {"command": "lean4.infoview.goToDefinition", "category": "Lean 4: InfoView", "title": "Go to Definition", "description": "This command is an implementation detail of the 'Go to Definition' context menu option in the infoview."}, {"command": "lean4.infoview.select", "category": "Lean 4: InfoView", "title": "Select", "description": "This command is an implementation detail of the 'Select' context menu option in the infoview."}, {"command": "lean4.infoview.unselect", "category": "Lean 4: InfoView", "title": "Unselect", "description": "This command is an implementation detail of the 'Unselect' context menu option in the infoview."}, {"command": "lean4.infoview.unselectAll", "category": "Lean 4: InfoView", "title": "Unselect All", "description": "This command is an implementation detail of the 'Unselect All' context menu option in the infoview."}, {"command": "lean4.infoview.pause", "category": "Lean 4: InfoView", "title": "Pause State", "description": "This command is an implementation detail of the 'Pause State' context menu option in the infoview."}, {"command": "lean4.infoview.unpause", "category": "Lean 4: InfoView", "title": "Unpause State", "description": "This command is an implementation detail of the 'Unpause State' context menu option in the infoview."}, {"command": "lean4.infoview.pin", "category": "Lean 4: InfoView", "title": "Pin State to Top", "description": "This command is an implementation detail of the 'Pin State to Top' context menu option in the infoview."}, {"command": "lean4.infoview.unpin", "category": "Lean 4: InfoView", "title": "Unpin State", "description": "This command is an implementation detail of the 'Unpin State' context menu option in the infoview."}, {"command": "lean4.infoview.refresh", "category": "Lean 4: InfoView", "title": "Refresh Paused State", "description": "This command is an implementation detail of the 'Refresh Paused State' context menu option in the infoview."}, {"command": "lean4.infoview.pauseAllMessages", "category": "Lean 4: InfoView", "title": "Pause 'All Messages'", "description": "This command is an implementation detail of the 'Pause 'All Messages'' context menu option in the infoview."}, {"command": "lean4.infoview.unpauseAllMessages", "category": "Lean 4: InfoView", "title": "Unpause 'All Messages'", "description": "This command is an implementation detail of the 'Unpause 'All Messages'' context menu option in the infoview."}, {"command": "lean4.infoview.goToPinnedLocation", "category": "Lean 4: InfoView", "title": "Go to Pinned Location", "description": "This command is an implementation detail of the 'Go to Pinned Location' context menu option in the infoview."}, {"command": "lean4.infoview.goToMessageLocation", "category": "Lean 4: InfoView", "title": "Go to Source Location of Message", "description": "This command is an implementation detail of the 'Go to Source Location of Message' context menu option in the infoview."}, {"command": "lean4.infoview.displayTargetBeforeAssumptions", "category": "Lean 4: InfoView", "title": "Display Target Before Assumptions", "description": "This command is an implementation detail of the 'Display Target Before Assumptions' context menu option in the infoview."}, {"command": "lean4.infoview.displayAssumptionsBeforeTarget", "category": "Lean 4: InfoView", "title": "Display Assumptions Before Target", "description": "This command is an implementation detail of the 'Display Assumptions Before Target' context menu option in the infoview."}, {"command": "lean4.infoview.hideTypeAssumptions", "category": "Lean 4: InfoView", "title": "Hide Type Assumptions", "description": "This command is an implementation detail of the 'Hide Type Assumptions' context menu option in the infoview."}, {"command": "lean4.infoview.showTypeAssumptions", "category": "Lean 4: InfoView", "title": "Show Type Assumptions", "description": "This command is an implementation detail of the 'Show Type Assumptions' context menu option in the infoview."}, {"command": "lean4.infoview.hideInstanceAssumptions", "category": "Lean 4: InfoView", "title": "Hide Instance Assumptions", "description": "This command is an implementation detail of the 'Hide Instance Assumptions' context menu option in the infoview."}, {"command": "lean4.infoview.showInstanceAssumptions", "category": "Lean 4: InfoView", "title": "Show Instance Assumptions", "description": "This command is an implementation detail of the 'Show Instance Assumptions' context menu option in the infoview."}, {"command": "lean4.infoview.hideInaccessibleAssumptions", "category": "Lean 4: InfoView", "title": "Hide Inaccessible Assumptions", "description": "This command is an implementation detail of the 'Hide Inaccessible Assumptions' context menu option in the infoview."}, {"command": "lean4.infoview.showInaccessibleAssumptions", "category": "Lean 4: InfoView", "title": "Show Inaccessible Assumptions", "description": "This command is an implementation detail of the 'Show Inaccessible Assumptions' context menu option in the infoview."}, {"command": "lean4.infoview.hideLetValues", "category": "Lean 4: InfoView", "title": "Hide Let-Values", "description": "This command is an implementation detail of the 'Hide Let-Values' context menu option in the infoview."}, {"command": "lean4.infoview.showLetValues", "category": "Lean 4: InfoView", "title": "Show Let-Values", "description": "This command is an implementation detail of the 'Show Let-Values' context menu option in the infoview."}, {"command": "lean4.infoview.hideGoalNames", "category": "Lean 4: InfoView", "title": "Hide Goal Names", "description": "This command is an implementation detail of the 'Hide Goal Names' context menu option in the infoview."}, {"command": "lean4.infoview.showGoalNames", "category": "Lean 4: InfoView", "title": "Show Goal Names", "description": "This command is an implementation detail of the 'Show Goal Names' context menu option in the infoview."}, {"command": "lean4.infoview.emphasizeFirstGoal", "category": "Lean 4: InfoView", "title": "Emphasize First Goal", "description": "This command is an implementation detail of the 'Emphasize First Goal' context menu option in the infoview."}, {"command": "lean4.infoview.deemphasizeFirstGoal", "category": "Lean 4: InfoView", "title": "De-Emphasize First Goal", "description": "This command is an implementation detail of the 'De-Emphasize First Goal' context menu option in the infoview."}, {"command": "lean4.infoview.saveSettings", "category": "Lean 4: InfoView", "title": "Save Current Settings to <PERSON><PERSON><PERSON> Settings", "description": "This command is an implementation detail of the 'Save Current Settings to Default Settings' context menu option in the infoview."}, {"command": "lean4.loogle.search", "category": "Lean 4: <PERSON><PERSON><PERSON>", "title": "Search With Loogle…", "description": "Show LoogleView"}, {"command": "lean4.moogle.search", "category": "Lean 4: <PERSON><PERSON><PERSON>", "title": "Search With Moogle…", "description": "Show MoogleView"}, {"command": "lean4.troubleshooting.showTroubleshootingGuide", "category": "Lean 4: <PERSON>shoot<PERSON>", "title": "Show Troubleshooting Guide", "description": "Show 'Troubleshooting' page in setup guide"}, {"command": "lean4.troubleshooting.showOutput", "category": "Lean 4: <PERSON>shoot<PERSON>", "title": "Show Troubleshooting Output", "description": "Show output channel containing all progress updates and errors of commands"}, {"command": "lean4.troubleshooting.showSetupInformation", "category": "Lean 4: <PERSON>shoot<PERSON>", "title": "Show Setup Information", "description": "Show setup information for the environment that the VS Code extension is running in"}, {"command": "lean4.docs.showSetupGuide", "category": "Lean 4: <PERSON><PERSON>", "title": "Show Setup Guide", "description": "Show 'Welcome' page containing a checklist of steps to install Lean 4"}, {"command": "lean4.docs.showExtensionManual", "category": "Lean 4: <PERSON><PERSON>", "title": "Show Manual", "description": "Show manual for the Lean 4 VS Code extension"}, {"command": "lean4.docs.showDocResources", "category": "Lean 4: <PERSON><PERSON>", "title": "Show Documentation Resources", "description": "Show available online documentation resources"}, {"command": "lean4.docs.showAbbreviations", "category": "Lean 4: <PERSON><PERSON>", "title": "Show Unicode Input Abbreviations", "description": "Shows all abbreviations for the unicode input mechanism"}, {"command": "lean4.setup.installElan", "category": "Lean 4: <PERSON><PERSON>", "title": "Install Elan", "description": "In<PERSON>l <PERSON><PERSON>'s version manager '<PERSON><PERSON>'"}, {"command": "lean4.setup.updateElan", "category": "Lean 4: <PERSON><PERSON>", "title": "Update <PERSON><PERSON>", "description": "Update <PERSON><PERSON>'s version manager '<PERSON><PERSON>' to the most recent version"}, {"command": "lean4.setup.uninstallElan", "category": "Lean 4: <PERSON><PERSON>", "title": "Uninstall Elan", "description": "Uninstall Lean's version manager '<PERSON><PERSON>' and all installed Lean versions"}, {"command": "lean4.setup.selectDefaultToolchain", "category": "Lean 4: <PERSON><PERSON>", "title": "Select Default Lean Version…", "description": "Sets a given Lean version to be the default for non-project files and command-line operations outside of projects"}, {"command": "lean4.setup.updateReleaseChannel", "category": "Lean 4: <PERSON><PERSON>", "title": "Update Release Channel Lean Version…", "description": "Updates the Lean version for a given release channel"}, {"command": "lean4.setup.uninstallToolchains", "category": "Lean 4: <PERSON><PERSON>", "title": "Uninstall Lean Versions…", "description": "Uninstalls given Lean versions"}, {"command": "lean4.project.createStandaloneProject", "category": "Lean 4: Project", "title": "Create Standalone Project…", "description": "Create a new Lean project that does not depend on any other projects"}, {"command": "lean4.project.createMathlibProject", "category": "Lean 4: Project", "title": "Create Project Using Mathlib…", "description": "Create a new Lean math formalization project using Mathlib"}, {"command": "lean4.project.open", "category": "Lean 4: Project", "title": "Open Local Project…", "description": "Opens a local Lean project"}, {"command": "lean4.project.clone", "category": "Lean 4: Project", "title": "Download Project…", "description": "Download an existing Lean project using `git clone`"}, {"command": "lean4.project.build", "category": "Lean 4: Project", "title": "Build Project", "description": "Build the current project"}, {"command": "lean4.project.clean", "category": "Lean 4: Project", "title": "Clean Project", "description": "Clean the current project, removing all build artifacts"}, {"command": "lean4.project.updateDependency", "category": "Lean 4: Project", "title": "Update Dependency…", "description": "Updates a dependency of the current project to the most recent version available for the branch pinned in 'lakefile.lean'."}, {"command": "lean4.project.fetchCache", "category": "Lean 4: Project", "title": "Fetch Mathlib Build Cache", "description": "Downloads cached Mathlib build artifacts to avoid full elaboration"}, {"command": "lean4.project.fetchFileCache", "category": "Lean 4: Project", "title": "Fetch Mathlib Build Cache For Current Imports", "description": "Downloads cached Mathlib build artifacts of the focused file and all of its imports to avoid full elaboration"}, {"command": "lean4.project.selectProjectToolchain", "category": "Lean 4: Project", "title": "Select Project Lean Version…", "description": "Updates the 'lean-toolchain' file of the currently focused project with a given Lean version"}, {"command": "lean4.leanModuleHierarchy.showModuleHierarchy", "category": "Lean 4: <PERSON><PERSON>le Hierarchy", "title": "Show Module Hierarchy", "description": "Updates the 'lean-toolchain' file of the currently focused project with a given Lean version"}, {"command": "lean4.leanModuleHierarchy.showInverseModuleHierarchy", "category": "Lean 4: <PERSON><PERSON>le Hierarchy", "title": "Show Inverse Module Hierarchy", "description": "Updates the 'lean-toolchain' file of the currently focused project with a given Lean version"}, {"command": "lean4.leanModuleHierarchy.refresh", "category": "Lean 4: <PERSON><PERSON>le Hierarchy", "title": "Refresh", "description": "Updates the module hierarchy to display the hierarchy for the current module", "icon": "$(refresh)"}, {"command": "lean4.leanModuleHierarchy.showImports", "category": "Lean 4: <PERSON><PERSON>le Hierarchy", "title": "Show Imports", "description": "Changes the mode of the module hierarchy to display the imports of a module", "icon": "$(call-outgoing)"}, {"command": "lean4.leanModuleHierarchy.showImportedBy", "category": "Lean 4: <PERSON><PERSON>le Hierarchy", "title": "Show Imported By", "description": "Changes the mode of the module hierarchy to display the modules that a module is imported by", "icon": "$(call-incoming)"}], "languages": [{"id": "lean4", "configuration": "./language-configuration.json", "extensions": [".lean"]}, {"id": "lean", "configuration": "./language-configuration.json"}, {"id": "lean4markdown", "aliases": [], "extensions": [".lean4markdown"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "lean4", "scopeName": "source.lean4", "path": "./syntaxes/lean4.json"}, {"language": "lean4markdown", "scopeName": "source.lean4.markdown", "path": "./syntaxes/lean4-markdown.json"}, {"scopeName": "markdown.lean4.codeblock", "path": "./syntaxes/codeblock.json", "injectTo": ["text.html.markdown"], "embeddedLanguages": {"meta.embedded.block.lean4": "lean4"}}, {"language": "lean", "scopeName": "source.lean4", "path": "./syntaxes/lean4.json"}], "keybindings": [{"command": "lean4.input.convert", "key": "tab", "mac": "tab", "when": "editorTextFocus && lean4.input.isActive"}, {"command": "lean4.restartFile", "key": "ctrl+shift+x", "mac": "cmd+shift+x", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.toggleInfoview", "key": "ctrl+shift+enter", "mac": "cmd+shift+enter", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.displayList", "key": "ctrl+shift+alt+enter", "mac": "cmd+shift+alt+enter", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.leanModuleHierarchy.showModuleHierarchy", "key": "shift+alt+m", "mac": "shift+alt+m", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.leanModuleHierarchy.showInverseModuleHierarchy", "key": "shift+alt+n", "mac": "shift+alt+n", "when": "lean4.isLeanFeatureSetActive"}], "colors": [{"id": "lean4.infoView.hypothesisName", "description": "The color used to display hypothesis names in the infoview", "defaults": {"light": "#cc7a00", "dark": "#ffcc00", "highContrast": "foreground", "highContrastLight": "foreground"}}, {"id": "lean4.infoView.inaccessibleHypothesisName", "description": "The color used to display inaccessible hypothesis names in the infoview", "defaults": {"light": "editor.foreground", "dark": "editor.foreground", "highContrast": "editor.foreground", "highContrastLight": "editor.foreground"}}, {"id": "lean4.infoView.goalCount", "description": "The color used to display the goal count in the infoview (e.g. \"1 goal\")", "defaults": {"light": "#367cb6", "dark": "#569cd6", "highContrast": "terminal.ansiBlue", "highContrastLight": "terminal.ansiBlue"}}, {"id": "lean4.infoView.turnstile", "description": "The color used to display the turnstile (⊢) in the infoview", "defaults": {"light": "#367cb6", "dark": "#569cd6", "highContrast": "terminal.ansiBlue", "highContrastLight": "terminal.ansiBlue"}}, {"id": "lean4.infoView.caseLabel", "description": "The color used to display the names of individual proof cases in the infoview", "defaults": {"light": "#1f7a1f", "dark": "#a1df90", "highContrast": "terminal.ansiGreen", "highContrastLight": "terminal.ansiGreen"}}], "menus": {"commandPalette": [{"command": "lean4.restartServer", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.stopServer", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.restartFile", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.refreshFileDependencies", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.redisplaySetupError", "when": "lean4.isStickyNotificationActiveButHidden"}, {"command": "lean4.input.convert", "when": "lean4.isLeanFeatureSetActive && lean4.input.isActive"}, {"command": "lean4.displayGoal", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.toggleInfoview", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.displayList", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.infoView.copyToComment", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.infoView.toggleStickyPosition", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.infoView.toggleUpdating", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.infoView.toggleExpectedType", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.infoview.goToDefinition", "when": "false"}, {"command": "lean4.infoview.select", "when": "false"}, {"command": "lean4.infoview.unselect", "when": "false"}, {"command": "lean4.infoview.unselectAll", "when": "false"}, {"command": "lean4.infoview.pause", "when": "false"}, {"command": "lean4.infoview.unpause", "when": "false"}, {"command": "lean4.infoview.pin", "when": "false"}, {"command": "lean4.infoview.unpin", "when": "false"}, {"command": "lean4.infoview.refresh", "when": "false"}, {"command": "lean4.infoview.pauseAllMessages", "when": "false"}, {"command": "lean4.infoview.unpauseAllMessages", "when": "false"}, {"command": "lean4.infoview.goToPinnedLocation", "when": "false"}, {"command": "lean4.infoview.goToMessageLocation", "when": "false"}, {"command": "lean4.infoview.displayTargetBeforeAssumptions", "when": "false"}, {"command": "lean4.infoview.displayAssumptionsBeforeTarget", "when": "false"}, {"command": "lean4.infoview.hideTypeAssumptions", "when": "false"}, {"command": "lean4.infoview.showTypeAssumptions", "when": "false"}, {"command": "lean4.infoview.hideInstanceAssumptions", "when": "false"}, {"command": "lean4.infoview.showInstanceAssumptions", "when": "false"}, {"command": "lean4.infoview.hideInaccessibleAssumptions", "when": "false"}, {"command": "lean4.infoview.showInaccessibleAssumptions", "when": "false"}, {"command": "lean4.infoview.hideLetValues", "when": "false"}, {"command": "lean4.infoview.showLetValues", "when": "false"}, {"command": "lean4.infoview.hideGoalNames", "when": "false"}, {"command": "lean4.infoview.showGoalNames", "when": "false"}, {"command": "lean4.infoview.emphasizeFirstGoal", "when": "false"}, {"command": "lean4.infoview.deemphasizeFirstGoal", "when": "false"}, {"command": "lean4.infoview.saveSettings", "when": "false"}, {"command": "lean4.loogle.search"}, {"command": "lean4.moogle.search"}, {"command": "lean4.troubleshooting.showTroubleshootingGuide"}, {"command": "lean4.troubleshooting.showOutput"}, {"command": "lean4.troubleshooting.showSetupInformation"}, {"command": "lean4.docs.showSetupGuide"}, {"command": "lean4.docs.showExtensionManual"}, {"command": "lean4.docs.showDocResources"}, {"command": "lean4.docs.showAbbreviations"}, {"command": "lean4.setup.installElan"}, {"command": "lean4.setup.updateElan"}, {"command": "lean4.setup.uninstallElan"}, {"command": "lean4.setup.selectDefaultToolchain"}, {"command": "lean4.setup.updateReleaseChannel"}, {"command": "lean4.setup.uninstallToolchains"}, {"command": "lean4.project.createStandaloneProject"}, {"command": "lean4.project.createMathlibProject"}, {"command": "lean4.project.open"}, {"command": "lean4.project.clone"}, {"command": "lean4.project.build", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.project.clean", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.project.updateDependency", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.project.fetchCache", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.project.fetchFileCache", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.project.selectProjectToolchain", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.leanModuleHierarchy.showModuleHierarchy", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.leanModuleHierarchy.showInverseModuleHierarchy", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.leanModuleHierarchy.refresh", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.leanModuleHierarchy.showImports", "when": "lean4.isLeanFeatureSetActive"}, {"command": "lean4.leanModuleHierarchy.showImportedBy", "when": "lean4.isLeanFeatureSetActive"}], "editor/title": [{"submenu": "lean4.titlebar", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "navigation@0"}], "lean4.titlebar": [{"submenu": "lean4.titlebar.newProject", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "1_setup@1"}, {"submenu": "lean4.titlebar.openProject", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "1_setup@1"}, {"command": "lean4.restartFile", "when": "lean4.isLeanFeatureSetActive", "group": "2_server@1"}, {"command": "lean4.restartServer", "when": "lean4.isLeanFeatureSetActive", "group": "2_server@2"}, {"command": "lean4.toggleInfoview", "when": "lean4.isLeanFeatureSetActive", "group": "3_infoview@1"}, {"command": "lean4.loogle.search", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "4_search@1"}, {"command": "lean4.moogle.search", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "4_search@2"}, {"command": "lean4.troubleshooting.showTroubleshootingGuide", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "5_troubleshooting@1"}, {"command": "lean4.troubleshooting.showOutput", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "5_troubleshooting@2"}, {"command": "lean4.troubleshooting.showSetupInformation", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "5_troubleshooting@3"}, {"submenu": "lean4.titlebar.versions", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "6_versions@1"}, {"submenu": "lean4.titlebar.projectActions", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "7_projectActions@1"}, {"submenu": "lean4.titlebar.documentation", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "8_documentation@1"}, {"command": "lean4.redisplaySetupError", "when": "lean4.isStickyNotificationActiveButHidden", "group": "9_setup<PERSON>rror@1"}], "lean4.titlebar.newProject": [{"command": "lean4.project.createStandaloneProject", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "1_newProject@1"}, {"command": "lean4.project.createMathlibProject", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "1_newProject@2"}], "lean4.titlebar.openProject": [{"command": "lean4.project.open", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "1_openProject@1"}, {"command": "lean4.project.clone", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "1_openProject@2"}], "lean4.titlebar.versions": [{"command": "lean4.setup.selectDefaultToolchain", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "1_toolchains@1"}, {"command": "lean4.setup.updateReleaseChannel", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "1_toolchains@2"}, {"command": "lean4.setup.uninstallToolchains", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "1_toolchains@3"}, {"command": "lean4.setup.installElan", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "2_elan@1"}, {"command": "lean4.setup.updateElan", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "2_elan@2"}, {"command": "lean4.setup.uninstallElan", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "2_<PERSON>an@3"}], "lean4.titlebar.projectActions": [{"command": "lean4.project.build", "when": "lean4.isLeanFeatureSetActive", "group": "1_projectActions@1"}, {"command": "lean4.project.clean", "when": "lean4.isLeanFeatureSetActive", "group": "1_projectActions@2"}, {"command": "lean4.project.updateDependency", "when": "lean4.isLeanFeatureSetActive", "group": "1_projectActions@3"}, {"command": "lean4.project.fetchCache", "when": "lean4.isLeanFeatureSetActive", "group": "2_mathlibActions@1"}, {"command": "lean4.project.fetchFileCache", "when": "lean4.isLeanFeatureSetActive", "group": "2_mathlibActions@2"}, {"command": "lean4.project.selectProjectToolchain", "when": "lean4.isLeanFeatureSetActive", "group": "3_projectToolchains@1"}], "lean4.titlebar.documentation": [{"command": "lean4.docs.showSetupGuide", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "1_docs@1"}, {"command": "lean4.docs.showExtensionManual", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "1_docs@2"}, {"command": "lean4.docs.showDocResources", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "1_docs@3"}, {"command": "lean4.docs.showAbbreviations", "when": "config.lean4.alwaysShowTitleBarMenu || lean4.isLeanFeatureSetActive", "group": "1_docs@4"}], "editor/context": [{"command": "lean4.leanModuleHierarchy.showModuleHierarchy", "when": "editorLangId == lean4", "group": "0_navigation@5"}, {"command": "lean4.leanModuleHierarchy.showInverseModuleHierarchy", "when": "editorLangId == lean4", "group": "0_navigation@6"}, {"command": "lean4.restartFile", "when": "editorLangId == lean4", "group": "z_commands@0"}, {"command": "lean4.loogle.search", "when": "editorLangId == lean4", "group": "z_commands@0"}], "webview/context": [{"command": "lean4.infoview.goToDefinition", "when": "webviewId == 'lean4_infoview' && interactiveCodeTagId", "group": "0_term@1"}, {"command": "lean4.infoview.goToPinnedLocation", "when": "webviewId == 'lean4_infoview' && goToPinnedLocationId", "group": "0_term@2"}, {"command": "lean4.infoview.goToMessageLocation", "when": "webviewId == 'lean4_infoview' && goToMessageLocationId", "group": "0_term@3"}, {"command": "lean4.infoview.select", "when": "webviewId == 'lean4_infoview' && selectableLocationId", "group": "0_term@4"}, {"command": "lean4.infoview.unselect", "when": "webviewId == 'lean4_infoview' && unselectableLocationId", "group": "0_term@5"}, {"command": "lean4.infoview.unselectAll", "when": "webviewId == 'lean4_infoview' && selectedLocationsId", "group": "0_term@6"}, {"command": "lean4.infoview.pin", "when": "webviewId == 'lean4_infoview' && pinId", "group": "1_info@1"}, {"command": "lean4.infoview.unpin", "when": "webviewId == 'lean4_infoview' && unpinId", "group": "1_info@2"}, {"command": "lean4.infoview.pause", "when": "webviewId == 'lean4_infoview' && pauseId", "group": "1_info@3"}, {"command": "lean4.infoview.unpause", "when": "webviewId == 'lean4_infoview' && unpauseId", "group": "1_info@4"}, {"command": "lean4.infoview.refresh", "when": "webviewId == 'lean4_infoview' && refreshId", "group": "1_info@5"}, {"command": "lean4.infoview.pauseAllMessages", "when": "webviewId == 'lean4_infoview' && pauseAllMessagesId", "group": "1_info@6"}, {"command": "lean4.infoview.unpauseAllMessages", "when": "webviewId == 'lean4_infoview' && unpauseAllMessagesId", "group": "1_info@7"}, {"submenu": "lean4.infoview.contextMenuSettings", "when": "webviewId == 'lean4_infoview'", "group": "z_settings@1"}], "lean4.infoview.contextMenuSettings": [{"command": "lean4.infoview.displayTargetBeforeAssumptions", "when": "webviewId == 'lean4_infoview' && displayTargetBeforeAssumptionsId", "group": "0_settings@1"}, {"command": "lean4.infoview.displayAssumptionsBeforeTarget", "when": "webviewId == 'lean4_infoview' && displayAssumptionsBeforeTargetId", "group": "0_settings@2"}, {"command": "lean4.infoview.hideTypeAssumptions", "when": "webviewId == 'lean4_infoview' && hideTypeAssumptionsId", "group": "0_settings@3"}, {"command": "lean4.infoview.showTypeAssumptions", "when": "webviewId == 'lean4_infoview' && showTypeAssumptionsId", "group": "0_settings@4"}, {"command": "lean4.infoview.hideInstanceAssumptions", "when": "webviewId == 'lean4_infoview' && hideInstanceAssumptionsId", "group": "0_settings@5"}, {"command": "lean4.infoview.showInstanceAssumptions", "when": "webviewId == 'lean4_infoview' && showInstanceAssumptionsId", "group": "0_settings@6"}, {"command": "lean4.infoview.hideInaccessibleAssumptions", "when": "webviewId == 'lean4_infoview' && hideInaccessibleAssumptionsId", "group": "0_settings@7"}, {"command": "lean4.infoview.showInaccessibleAssumptions", "when": "webviewId == 'lean4_infoview' && showInaccessibleAssumptionsId", "group": "0_settings@8"}, {"command": "lean4.infoview.hideLetValues", "when": "webviewId == 'lean4_infoview' && hideLetValuesId", "group": "0_settings@9"}, {"command": "lean4.infoview.showLetValues", "when": "webviewId == 'lean4_infoview' && showLetValuesId", "group": "0_settings@10"}, {"command": "lean4.infoview.hideGoalNames", "when": "webviewId == 'lean4_infoview' && hideGoalNamesId", "group": "0_settings@11"}, {"command": "lean4.infoview.showGoalNames", "when": "webviewId == 'lean4_infoview' && showGoalNamesId", "group": "0_settings@12"}, {"command": "lean4.infoview.emphasizeFirstGoal", "when": "webviewId == 'lean4_infoview' && emphasizeFirstGoalId", "group": "0_settings@13"}, {"command": "lean4.infoview.deemphasizeFirstGoal", "when": "webviewId == 'lean4_infoview' && deemphasizeFirstGoalId", "group": "0_settings@14"}, {"command": "lean4.infoview.saveSettings", "when": "webviewId == 'lean4_infoview' && saveSettingsId", "group": "1_save@1"}], "view/title": [{"command": "lean4.leanModuleHierarchy.showImports", "when": "view == leanModuleHierarchy && lean4.leanModuleHierarchy.mode == 'ImportedBy'", "group": "navigation@0"}, {"command": "lean4.leanModuleHierarchy.showImportedBy", "when": "view == leanModuleHierarchy && lean4.leanModuleHierarchy.mode == 'Imports'", "group": "navigation@1"}, {"command": "lean4.leanModuleHierarchy.refresh", "when": "view == leanModuleHierarchy", "group": "navigation@2"}]}, "submenus": [{"id": "lean4.titlebar", "label": "Lean 4", "icon": {"dark": "./media/lean-mini-dark.svg", "light": "./media/lean-mini-light.svg"}}, {"id": "lean4.titlebar.newProject", "label": "New Project…"}, {"id": "lean4.titlebar.openProject", "label": "Open Project…"}, {"id": "lean4.titlebar.versions", "label": "Version Management…"}, {"id": "lean4.titlebar.projectActions", "label": "Project Actions…"}, {"id": "lean4.titlebar.documentation", "label": "Documentation…"}, {"id": "lean4.infoview.contextMenuSettings", "label": "Settings…"}], "views": {"explorer": [{"id": "leanModuleHierarchy", "name": "Module Hierarchy", "type": "tree", "when": "lean4.isLeanFeatureSetActive && lean4.leanModuleHierarchy.visible", "icon": "$(type-hierarchy-sub)"}]}, "semanticTokenScopes": [{"scopes": {"keyword": ["keyword.other"]}}], "configurationDefaults": {"files.readonlyInclude": {"**/.lake/**/*.lean": true, "**/.elan/**/*.lean": true}, "[lean4]": {"editor.unicodeHighlight.ambiguousCharacters": false, "editor.tabSize": 2, "editor.insertSpaces": true, "editor.wordSeparators": "`~@$%^&*()-=+[{]}⟨⟩⦃⦄⟦⟧⟮⟯‹›\\|;:\",.<>/", "files.encoding": "utf8", "files.eol": "\n", "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true}}, "walkthroughs": [{"id": "lean4.welcome", "title": "Lean 4 Setup", "description": "Getting started with Lean 4\n", "steps": [{"id": "lean4.welcome.openSetupGuide", "title": "Re-Open Setup Guide", "description": "This guide can always be re-opened by opening an empty file, clicking on the ∀-symbol in the top right and selecting 'Documentation…' > 'Show Setup Guide'.", "media": {"image": "media/open-setup-guide.png", "altText": "Click on the ∀-symbol in the top right and select 'Documentation…' > 'Show Setup Guide'."}}, {"id": "lean4.welcome.documentation", "title": "Books and Documentation", "description": "Learn using Lean 4 with the resources on the right.", "media": {"markdown": "./media/guide-documentation.md"}}, {"id": "lean4.welcome.installDeps.linux", "title": "Install Required Dependencies", "description": "Install Git and curl using your package manager.", "media": {"markdown": "./media/guide-installDeps-linux.md"}, "when": "isLinux"}, {"id": "lean4.welcome.installDeps.mac", "title": "Install Required Dependencies", "description": "Install Homebrew, Git and curl.", "media": {"markdown": "./media/guide-installDeps-mac.md"}, "when": "isMac"}, {"id": "lean4.welcome.installDeps.windows", "title": "Install Required Dependencies", "description": "Install Git.", "media": {"markdown": "./media/guide-installDeps-windows.md"}, "when": "isWindows"}, {"id": "lean4.welcome.installElan.unix", "title": "Install Lean Version Manager", "description": "Install Lean's version manager <PERSON><PERSON>.\n[Click to install](command:lean4.setup.installElan)", "media": {"markdown": "./media/guide-installElan-unix.md"}, "when": "isLinux || isMac"}, {"id": "lean4.welcome.installElan.windows", "title": "Install Lean Version Manager", "description": "Install Lean's version manager <PERSON><PERSON>.\n[Click to install](command:lean4.setup.installElan)", "media": {"markdown": "./media/guide-installElan-windows.md"}, "when": "isWindows"}, {"id": "lean4.welcome.setupProject", "title": "Set Up Lean 4 Project", "description": "Set up a Lean 4 project by clicking on one of the options on the right.", "media": {"markdown": "./media/guide-setupProject.md"}}, {"id": "lean4.welcome.help", "title": "Questions and Troubleshooting", "description": "If you have any questions or are having trouble with any of the previous steps, please visit us on the [Lean Zulip chat](https://leanprover.zulipchat.com/) so that we can help you.", "media": {"markdown": "./media/guide-help.md"}}]}]}, "extensionKind": ["workspace"], "activationEvents": ["onLanguage:markdown", "onStartupFinished"], "main": "./dist/extension", "scripts": {"vscode:prepublish": "webpack --env production", "build": "webpack --env production", "watch": "webpack --env development --watch", "watchTest": "concurrently \"tsc -p . -w --outDir out\" \"npm run watch\"", "package": "vsce package", "packagePreRelease": "vsce package --pre-release", "pretest": "tsc -p . --outDir out", "test": "node ./out/test/suite/runTest.js"}, "dependencies": {"@leanprover/infoview": "~0.8.5", "@leanprover/infoview-api": "~0.7.0", "@leanprover/unicode-input": "~0.1.4", "@leanprover/unicode-input-component": "~0.1.4", "@vscode/codicons": "^0.0.36", "@vscode-elements/elements": "^1.7.1", "markdown-it": "^14.1.0", "markdown-it-anchor": "^9.0.1", "semver": "^7.6.0", "vscode-languageclient": "^8.0.2", "zod": "^3.22.4"}, "devDependencies": {"@types/markdown-it": "^14.1.1", "@types/mocha": "^10.0.6", "@types/node": "^20.12.12", "@types/semver": "^7.5.7", "@types/vscode": "^1.75.0", "@types/vscode-webview": "^1.57.5", "@vscode/test-electron": "^2.3.9", "@vscode/vsce": "^2.21.1", "concurrently": "^8.2.2", "copy-webpack-plugin": "^12.0.2", "mocha": "^10.3.0", "ovsx": "^0.9.1", "source-map-loader": "^5.0.0", "ts-loader": "^9.5.1", "typescript": "^5.4.5", "webpack": "^5.90.3", "webpack-cli": "^5.1.4", "glob": "^10.4.5"}, "icon": "images/lean_logo.png", "license": "Apache-2.0", "__metadata": {"id": "ddb6b8b1-9e92-40ce-90d4-45bb5f345ef9", "publisherDisplayName": "leanprover", "publisherId": "36715126-cec3-4ee0-926f-91a098ea5112", "isPreReleaseVersion": false}}