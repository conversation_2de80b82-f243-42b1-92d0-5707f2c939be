{"compilerOptions": {"experimentalDecorators": true, "resolveJsonModule": true, "module": "commonjs", "target": "es6", "outDir": "out", "lib": ["es6", "ES2021", "es2020.string"], "sourceMap": true, "alwaysStrict": true, "rootDir": "./", "noImplicitAny": true, "noFallthroughCasesInSwitch": true, "strictNullChecks": true, "esModuleInterop": true}, "include": ["src/**/*", "test/suite/**/*"], "exclude": ["node_modules", ".vscode-test"]}