{"fileTypes": [], "injectionSelector": "L:text.html.markdown", "patterns": [{"include": "#lean-code-block"}], "repository": {"lean-code-block": {"begin": "(^|\\G)(\\s*)(\\`{3,}|~{3,})\\s*(?i:(lean)(\\s+[^`~]*)?$)", "name": "markup.fenced_code.block.markdown", "end": "(^|\\G)(\\2|\\s{0,3})(\\3)\\s*$", "beginCaptures": {"3": {"name": "punctuation.definition.markdown"}, "5": {"name": "fenced_code.block.language"}, "6": {"name": "fenced_code.block.language.attributes"}}, "endCaptures": {"3": {"name": "punctuation.definition.markdown"}}, "patterns": [{"begin": "(^|\\G)(\\s*)(.*)", "while": "(^|\\G)(?!\\s*([`~]{3,})\\s*$)", "contentName": "meta.embedded.block.lean4", "patterns": [{"include": "source.lean4"}]}]}}, "scopeName": "markdown.lean4.codeblock"}