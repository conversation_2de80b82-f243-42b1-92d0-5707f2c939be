import { Definition, DefinitionProvider, Location, Position, Range, TextDocument, Uri, CancellationToken, languages } from 'vscode'
import { LeanClientProvider } from '../utils/clientProvider'
import { parseExtUri, toExtUri } from '../utils/exturi'

/**
 * Provides "Go to Definition" support (Ctrl+Click/F12) for module names that
 * appear inside `import` statements.  We query the Lean language server via the
 * custom module-hierarchy requests that were introduced in Lean 4.22.  For
 * earlier versions of Lean that do not offer this capability, the provider
 * silently falls back to letting other definition providers handle the
 * request.
 */
export class ImportDefinitionProvider implements DefinitionProvider {
    constructor(private readonly clientProvider: LeanClientProvider) {}

    async provideDefinition(
        document: TextDocument,
        position: Position,
        _token: CancellationToken,
    ): Promise<Definition | undefined> {
        // Only act on Lean 4 files.
        if (document.languageId !== 'lean4' && document.languageId !== 'lean') {
            return undefined
        }

        const line = document.lineAt(position.line).text
        // We are only interested in lines that start with `import`.
        // Using a simple heuristic here as Lean import lines are always at the
        // beginning of a statement.
        if (!/^\s*import\b/.test(line)) {
            return undefined
        }

        // Determine the module name under the cursor.
        const wordRange = document.getWordRangeAtPosition(position, /[A-Za-z0-9_.]+/)
        if (!wordRange) {
            return undefined
        }
        const moduleName = document.getText(wordRange)

        // Convert the TextDocument URI to the extension-internal URI type.
        const extUri = toExtUri(document.uri)
        if (!extUri) {
            return undefined
        }

        // Look up the corresponding Lean language client.
        const client = this.clientProvider.findClient(extUri)
        if (!client) {
            return undefined
        }

        // Ask the server for the module hierarchy information of the current
        // file so that we can resolve the imported modules.
        const prep = await client.sendPrepareModuleHierarchy(extUri)
        if (prep.kind !== 'Success' || !prep.module) {
            return undefined
        }

        const imports = await client.sendModuleHierarchyImports(prep.module)
        if (imports.kind !== 'Success') {
            return undefined
        }

        const match = imports.imports.find(i => i.module.name === moduleName)
        if (!match) {
            return undefined
        }

        const targetUri = parseExtUri(match.module.uri)?.asUri() ?? Uri.parse(match.module.uri)
        // We jump to the beginning of the target file.
        return new Location(targetUri, new Position(0, 0))
    }
}
