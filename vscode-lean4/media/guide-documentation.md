## Books
If you want to learn Lean 4, choose one of the following introductory books based on your background. If you are getting stuck or have any questions, click on the 'Questions and Troubleshooting' step at the bottom on the left side.

- [Functional Programming in Lean](https://lean-lang.org/functional_programming_in_lean/)  
  The standard introduction for using Lean 4 as a general-purpose programming language.
- [The Mechanics of Proof](https://hrmacbeth.github.io/math2001/)  
  An introduction to Lean 4 as an interactive theorem prover for anyone who also wants to learn how to write rigorous mathematical proofs.
- [Mathematics in Lean](https://leanprover-community.github.io/mathematics_in_lean/)  
  The standard introduction to Lean 4 as an interactive theorem prover for users with a mathematics background.
- [Theorem Proving in Lean 4](https://lean-lang.org/theorem_proving_in_lean4/)  
  The standard reference for using Lean 4 as an interactive theorem prover. Suited as an introduction for users with a computer science background, advanced users and for general use as a reference manual.

Once you have completed one of these books and its exercises, you are ready to use Lean 4 for your own projects. If you want to use Lean 4 both as a general-purpose programming language and an interactive theorem prover, it is recommended to read both [Functional Programming in Lean](https://lean-lang.org/functional_programming_in_lean/) and [Theorem Proving in Lean 4](https://lean-lang.org/theorem_proving_in_lean4/).

## Hands-On Tutorial
If you want to dive right into using Lean 4 to prove elementary theorems about natural numbers, you can play the [Natural Number Game](https://adam.math.hhu.de/#/g/hhu-adam/NNG4). It can be played online using your browser without a local installation.

## Using Lean 4 in VS Code

The [Lean 4 VS Code extension manual](command:lean4.docs.showExtensionManual) covers all kinds of essential tools when working with Lean 4 in VS Code. Even if you are already familiar with other programming languages and VS Code in general, you may find the first five subsections of the 'Interacting with Lean files' section in the manual to be very helpful. 

## Additional Resources
**Website**  
[Lean's website](https://lean-lang.org/) links to learning resources, publications, talks and articles about Lean.

**Lean Community**  
The [Lean Community website](https://leanprover-community.github.io/index.html) links to several other helpful learning resources not listed here and provides an introduction to [Mathlib](https://github.com/leanprover-community/mathlib4), Lean's math library.

**Manual**  
The [Lean Manual](https://lean-lang.org/lean4/doc/) documents several features of Lean 4 and can be consulted for some of the more technical details concerning Lean.

## Finding Definitions and Theorems
There are two search engines that help you find definitions and theorems in [Mathlib](https://github.com/leanprover-community/mathlib4), [Lean's standard library](https://github.com/leanprover/std4) and Lean 4 itself:

- [Loogle](command:lean4.loogle.search): A pattern-based search engine that finds definitions and theorems by type signature or by the names of their identifiers.
- [Moogle](https://www.moogle.ai/): An AI-based search engine that finds definitions and theorems by a description in natural language.
