## <PERSON><PERSON>
[<PERSON><PERSON>](https://github.com/leanprover/elan) automatically manages all the different versions of Lean and ensures that the correct version is used when opening a project.

Clicking [this link](command:lean4.setup.installElan) will download the [<PERSON><PERSON> setup script](https://github.com/leanprover/elan/blob/master/elan-init.ps1) and execute it.

If the script executes without displaying an error, <PERSON><PERSON> has successfully been installed. If it displays an error that you do not understand, click on the 'Questions and Troubleshooting' step on the left.
