.loader {
    display: inline-block;
    margin-left: 8px;
    border: 4px solid #f3f3f3; /* Light grey */
    border-top: 4px solid #3498db; /* Blue */
    border-radius: 50%;
    width: 16px;
    height: 16px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.hidden {
    display: none;
}

.query-label {
    display: block;
    color: var(--vscode-foreground);
    cursor: pointer;
    font-size: var(--vscode-font-size);
    line-height: normal;
    margin-bottom: 2px;
}

.query-container {
    display: flex;
    align-items: center;
}

.input-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 70ch;

    box-sizing: border-box;
    position: relative;
    flex-direction: row;
    color: var(--vscode-input-foreground);
    background: var(--vscode-input-background);
    border-radius: 2px;
    border: 1px solid var(--vscode-dropdown-border);
    height: 26px;
    min-width: 100px;
    font-family: var(--vscode-font-family);
    outline: none;
    user-select: none;
}

.input-container:focus-within:not([disabled]) {
    border-color: var(--vscode-focusBorder);
}

[contenteditable='true'].single-line {
    white-space: nowrap;
    width: 60ch;
    overflow: hidden;
    outline: none;

    height: calc(100% - 4px);
    margin-top: auto;
    margin-bottom: auto;
    border: none;
    padding: 0 9px;
    font-size: var(--vscode-font-size);
    line-height: normal;
}
[contenteditable='true'].single-line br {
    display: none;
}
[contenteditable='true'].single-line * {
    display: inline;
    white-space: nowrap;
}

vscode-button {
    vertical-align: middle;
}

body {
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-font-size);
    color: var(--vscode-foreground);
    padding: 10px;
    line-height: 1.6;
}

h1,
h2 {
    color: var(--vscode-foreground);
    font-weight: normal;
}

.result-item {
    border: 1px solid rgba(209, 213, 219, 0.5);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    background-color: var(--vscode-editor-background);
    padding: 10px;
}

.result-header {
    cursor: pointer;
    user-select: none;
    padding: 0.625rem 1rem;
    background-color: var(--vscode-editor-background);
    border-bottom: 1px solid var(--vscode-panel-border);
}
.result-content {
    display: none;
    margin-top: 10px;
    padding: 1rem;
}
.result-content.open {
    display: block;
}

.result-content.open {
    display: block;
}

.result-header h3 {
    margin: 0;
    font-size: 0.875rem;
    font-weight: 500;
    font-family: var(--vscode-editor-font-family);
}

.display-html-container {
    font-family: var(--vscode-editor-font-family);
}

.display-html-container a {
    text-decoration: none;
    color: var(--vscode-editor-foreground);
}

.display-html-container a:hover {
    text-decoration: underline;
}

vscode-link {
    margin-top: 5px;
}
.decl_header {
    display: block;
    margin-bottom: 0.5em;
    color: var(--vscode-editor-foreground);
}

.decl_kind {
    color: var(--vscode-keyword-foreground);
    font-weight: bold;
}

.decl_name {
    color: var(--vscode-symbolIcon-functionForeground);
    font-weight: bold;
}

.name {
    color: var(--vscode-symbolIcon-variableForeground);
}

.decl_args {
    color: var(--vscode-editor-foreground);
}

.decl_type {
    color: var(--vscode-symbolIcon-typeParameterForeground);
    text-indent: 20px;
}

.fn {
    color: var(--vscode-symbolIcon-functionForeground);
}

.impl_arg {
    font-style: italic;
}

.decl_extends,
.decl_parent {
    color: var(--vscode-keyword-foreground);
}

.break_within {
    word-break: break-word;
}
