<label class="query-label" for="query-text-field">Moogle query</label>
<div class="query-container">
    <div class="input-container">
        <div id="query-text-field" class="single-line" contenteditable="true"></div>
        <vscode-icon id="previous-query-button" name="arrow-left" label="Load Previous Query" action-icon></vscode-icon>
        <vscode-icon id="next-query-button" name="arrow-right" label="Load Next Query" action-icon></vscode-icon>
        <vscode-icon id="find-button" name="search" label="Send Loogle Query" action-icon></vscode-icon>
    </div>
    <div id="spinner" class="loader hidden"></div>
</div>

<p id="header" hidden></p>

<p id="error" hidden></p>

<h2 id="result-header" hidden>Results</h2>
<div id="results"></div>

<h1>Moogle — Find theorems, faster</h1>

<p>Moogle is a semantic search engine designed to help you find theorems and definitions in Mathlib4 quickly. It is the result of a collaboration between Morph Labs and the Lean community.</p>

<p>For a web version of this tool, visit <a href="https://www.moogle.ai" target="_blank">moogle.ai</a>.</p>

<a id="close-tab" href="command:workbench.action.closeActiveEditor" hidden />
