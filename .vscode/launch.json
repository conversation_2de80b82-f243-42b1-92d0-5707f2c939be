// A launch configuration that compiles the extension and then opens it inside a new window
{
    "version": "0.1.0",
    "configurations": [
        {
            "name": "Launch Extension",
            "type": "extensionHost",
            "request": "launch",
            "runtimeExecutable": "${execPath}",
            "args": ["--extensionDevelopmentPath=${workspaceRoot}/vscode-lean4"],
            "stopOnEntry": false,
            "sourceMaps": true,
            "outFiles": ["${workspaceRoot}/vscode-lean4/dist/**/*.js"],
            "preLaunchTask": "watch",
            "debugWebviews": true,
            "rendererDebugOptions": {
                "webRoot": "${workspaceRoot}/vscode-lean4/media"
            }
        },
        {
            "name": "Extension Tests - bootstrap tests",
            "type": "extensionHost",
            "request": "launch",
            "runtimeExecutable": "${execPath}",
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}/vscode-lean4",
                "--extensionTestsPath=${workspaceFolder}/vscode-lean4/out/test/suite/index"
            ],
            "env": {
                "LEAN4_TEST_FOLDER": "bootstrap"
            },
            "cwd": "${workspaceFolder}/vscode-lean4/out/",
            "outFiles": ["${workspaceFolder}/vscode-lean4/out/test/suite/**/*.js"],
            "preLaunchTask": "watchTest"
        },
        {
            "name": "Extension Tests - lean3",
            "type": "extensionHost",
            "request": "launch",
            "runtimeExecutable": "${execPath}",
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}/vscode-lean4",
                "--extensionTestsPath=${workspaceFolder}/vscode-lean4/out/test/suite/index"
            ],
            "env": {
                "LEAN4_TEST_FOLDER": "lean3"
            },
            "cwd": "${workspaceFolder}/vscode-lean4/out/",
            "outFiles": ["${workspaceFolder}/vscode-lean4/out/test/suite/**/*.js"],
            "preLaunchTask": "watchTest"
        },
        {
            "name": "Extension Tests - infoview",
            "type": "extensionHost",
            "request": "launch",
            "runtimeExecutable": "${execPath}",
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}/vscode-lean4",
                "--extensionTestsPath=${workspaceFolder}/vscode-lean4/out/test/suite/index"
            ],
            "env": {
                "LEAN4_TEST_FOLDER": "info"
            },
            "cwd": "${workspaceFolder}/vscode-lean4/out/",
            "outFiles": ["${workspaceFolder}/vscode-lean4/out/test/suite/**/*.js"],
            "preLaunchTask": "watchTest"
        },
        {
            "name": "Extension Tests - adhoc",
            "type": "extensionHost",
            "request": "launch",
            "runtimeExecutable": "${execPath}",
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}/vscode-lean4",
                "--extensionTestsPath=${workspaceFolder}/vscode-lean4/out/test/suite/index"
            ],
            "env": {
                "LEAN4_TEST_FOLDER": "simple"
            },
            "cwd": "${workspaceFolder}/vscode-lean4/out/",
            "outFiles": ["${workspaceFolder}/vscode-lean4/out/test/suite/**/*.js"],
            "preLaunchTask": "watchTest"
        },
        {
            "name": "Extension Tests - simple",
            "type": "extensionHost",
            "request": "launch",
            "runtimeExecutable": "${execPath}",
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}/vscode-lean4",
                "--extensionTestsPath=${workspaceFolder}/vscode-lean4/out/test/suite/index",
                "${workspaceFolder}/vscode-lean4/test/test-fixtures/simple"
            ],
            "env": {
                "LEAN4_TEST_FOLDER": "simple"
            },
            "cwd": "${workspaceFolder}/vscode-lean4/out/",
            "outFiles": ["${workspaceFolder}/vscode-lean4/out/test/suite/**/*.js"],
            "preLaunchTask": "watchTest"
        },
        {
            "name": "Extension Tests - toolchains",
            "type": "extensionHost",
            "request": "launch",
            "runtimeExecutable": "${execPath}",
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}/vscode-lean4",
                "--extensionTestsPath=${workspaceFolder}/vscode-lean4/out/test/suite/index",
                "${workspaceFolder}/vscode-lean4/test/test-fixtures/simple"
            ],
            "env": {
                "LEAN4_TEST_FOLDER": "toolchains"
            },
            "cwd": "${workspaceFolder}/vscode-lean4/out/",
            "outFiles": ["${workspaceFolder}/vscode-lean4/out/test/suite/**/*.js"],
            "preLaunchTask": "watchTest"
        },
        {
            "name": "Extension Tests - restarts",
            "type": "extensionHost",
            "request": "launch",
            "runtimeExecutable": "${execPath}",
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}/vscode-lean4",
                "--extensionTestsPath=${workspaceFolder}/vscode-lean4/out/test/suite/index",
                "${workspaceFolder}/vscode-lean4/test/test-fixtures/simple"
            ],
            "env": {
                "LEAN4_TEST_FOLDER": "restarts"
            },
            "cwd": "${workspaceFolder}/vscode-lean4/out/",
            "outFiles": ["${workspaceFolder}/vscode-lean4/out/test/suite/**/*.js"],
            "preLaunchTask": "watchTest"
        },
        {
            "name": "Extension Tests - multi-folder",
            "type": "extensionHost",
            "request": "launch",
            "runtimeExecutable": "${execPath}",
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}/vscode-lean4",
                "--extensionTestsPath=${workspaceFolder}/vscode-lean4/out/test/suite/index",
                "${workspaceFolder}/vscode-lean4/test/test-fixtures/multi/multi.code-workspace"
            ],
            "env": {
                "LEAN4_TEST_FOLDER": "multi"
            },
            "cwd": "${workspaceFolder}/vscode-lean4/out/",
            "outFiles": ["${workspaceFolder}/vscode-lean4/out/test/suite/**/*.js"],
            "preLaunchTask": "watchTest"
        },
        {
            "name": "Launch runTests.js",
            "program": "${workspaceFolder}/vscode-lean4/out/test/suite/runTest.js",
            "request": "launch",
            "skipFiles": ["<node_internals>/**"],
            "cwd": "${workspaceFolder}/vscode-lean4/",
            "sourceMaps": true,
            "type": "node"
        }
    ]
}
