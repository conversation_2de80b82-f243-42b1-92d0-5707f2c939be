// See https://go.microsoft.com/fwlink/?LinkId=733558
// for the documentation about the tasks.json format
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "watch",
            "type": "npm",
            "script": "watch",
            "problemMatcher": {
                "owner": "typescript",
                "source": "ts",
                "fileLocation": "absolute",
                "applyTo": "closedDocuments",
                "pattern": [
                    {
                        "regexp": "\\[tsl\\] (ERROR|WARNING) in (.*)?\\((\\d+),(\\d+)\\)",
                        "severity": 1,
                        "file": 2,
                        "line": 3,
                        "column": 4
                    },
                    {
                        "regexp": "\\s*TS(\\d+):\\s*(.*)$",
                        "code": 1,
                        "message": 2
                    }
                ],
                "background": {
                    "activeOnStart": true,
                    "beginsPattern": "^extension:",
                    "endsPattern": "webpack .* compiled .* in "
                }
            },
            "isBackground": true,
            "presentation": {
                "reveal": "never"
            },
            "group": "build"
        },
        {
            "label": "watchTest",
            "type": "npm",
            "script": "watchTest",
            "problemMatcher": {
                "owner": "typescript",
                "source": "ts",
                "fileLocation": "absolute",
                "applyTo": "closedDocuments",
                "pattern": [
                    {
                        "regexp": "\\[tsl\\] (ERROR|WARNING) in (.*)?\\((\\d+),(\\d+)\\)",
                        "severity": 1,
                        "file": 2,
                        "line": 3,
                        "column": 4
                    },
                    {
                        "regexp": "\\s*TS(\\d+):\\s*(.*)$",
                        "code": 1,
                        "message": 2
                    }
                ],
                "background": {
                    "activeOnStart": true,
                    "beginsPattern": "^extension:",
                    "endsPattern": "webpack .* compiled .* in "
                }
            },
            "isBackground": true,
            "presentation": {
                "reveal": "never"
            },
            "group": "build"
        },
        {
            "label": "build",
            "type": "npm",
            "script": "build",
            "problemMatcher": {
                "owner": "typescript",
                "source": "ts",
                "fileLocation": "absolute",
                "applyTo": "closedDocuments",
                "pattern": [
                    {
                        "regexp": "\\[tsl\\] (ERROR|WARNING) in (.*)?\\((\\d+),(\\d+)\\)",
                        "severity": 1,
                        "file": 2,
                        "line": 3,
                        "column": 4
                    },
                    {
                        "regexp": "\\s*TS(\\d+):\\s*(.*)$",
                        "code": 1,
                        "message": 2
                    }
                ]
            },
            "presentation": {
                "reveal": "never"
            },
            "group": {
                "kind": "build",
                "isDefault": true
            }
        }
    ]
}
