// Place your settings in this file to overwrite default and user settings.
{
    "search.exclude": {
        "**/out": true, // set this to false to include "out" folder in search results
        "**/dist": true, // set this to false to include "dist" folder in search results
        "**/.vscode-test": true, // set this to false to include ".vscode-test" folder in search results
        "**/node_modules": true // set this to false to include "node_modules" folder in search results
    },
    "typescript.tsdk": "./node_modules/typescript/lib", // we want to use the TS server from our node_modules folder to control its version
    "files.insertFinalNewline": true,
    "files.trimTrailingWhitespace": true,
    "[markdown]": {
        "files.trimTrailingWhitespace": false
    }
}
