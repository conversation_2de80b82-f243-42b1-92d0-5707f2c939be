{"name": "@leanprover/infoview", "version": "0.8.5", "description": "An interactive display for the Lean 4 theorem prover.", "scripts": {"watch": "rollup --config --environment NODE_ENV:development --watch", "build": "rollup --config --environment NODE_ENV:production", "watchTest": "npm run watch", "test": "tsc -p test/tsconfig.json"}, "exports": {".": {"default": "./dist/index.production.min.js", "types": "./dist/index.d.ts"}, "./loader": {"default": "./dist/loader.production.min.js", "types": "./dist/loader.d.ts"}, "./package.json": "./package.json"}, "files": ["dist/*"], "type": "module", "license": "Apache-2.0", "devDependencies": {"@floating-ui/react": "^0.26.25", "@rollup/plugin-commonjs": "^28.0.0", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.1.0", "@rollup/plugin-replace": "^6.0.1", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.0", "@rollup/plugin-url": "^8.0.1", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-syntax-highlighter": "^15.5.13", "current-release": "npm:@leanprover/infoview@latest", "highlightjs-lean": "^1.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "remark-math": "^6.0.0", "rehype-mathjax": "^6.0.0", "rollup": "^4.24.0", "rollup-plugin-css-only": "^4.3.0", "typescript": "^5.4.5"}, "dependencies": {"@leanprover/infoview-api": "~0.7.0", "@vscode/codicons": "^0.0.32", "@vscode-elements/react-elements": "^0.5.0", "es-module-lexer": "^1.5.4", "es-module-shims": "^1.7.3", "react-fast-compare": "^3.2.2", "tachyons": "^4.12.0", "vscode-languageserver-protocol": "^3.17.3"}}