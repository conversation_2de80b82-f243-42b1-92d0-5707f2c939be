/** @module See `rollup.config.js` for what this file does. */
import * as React from 'react'
export default React
// We need to explicitly list all the names because 'react' is a CommonJS module for which
// `export * from` does not work.
import {
    Children,
    cloneElement,
    Component,
    createContext,
    createElement,
    createFactory,
    createRef,
    forwardRef,
    Fragment,
    isValidElement,
    lazy,
    memo,
    Profiler,
    PureComponent,
    startTransition,
    StrictMode,
    Suspense,
    // @ts-ignore
    unstable_act,
    useCallback,
    useContext,
    useDebugValue,
    useDeferredValue,
    useEffect,
    useId,
    useImperativeHandle,
    useInsertionEffect,
    useLayoutEffect,
    useMemo,
    useReducer,
    useRef,
    useState,
    useSyncExternalStore,
    useTransition,
    version,
    // @ts-ignore
    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,
} from 'react'
export {
    Children,
    Component,
    Fragment,
    Profiler,
    PureComponent,
    StrictMode,
    Suspense,
    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,
    cloneElement,
    createContext,
    createElement,
    createFactory,
    createRef,
    forwardRef,
    isValidElement,
    lazy,
    memo,
    startTransition,
    unstable_act,
    useCallback,
    useContext,
    useDebugValue,
    useDeferredValue,
    useEffect,
    useId,
    useImperativeHandle,
    useInsertionEffect,
    useLayoutEffect,
    useMemo,
    useReducer,
    useRef,
    useState,
    useSyncExternalStore,
    useTransition,
    version,
}
