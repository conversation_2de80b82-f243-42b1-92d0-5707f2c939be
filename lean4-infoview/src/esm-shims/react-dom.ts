/** @module See `rollup.config.js` for what this file does. */
import * as ReactDOM from 'react-dom'
import {
    createPortal,
    // @ts-ignore
    createRoot,
    findDOMNode,
    flushSync,
    hydrate,
    // @ts-ignore
    hydrateRoot,
    render,
    unmountComponentAtNode,
    unstable_batchedUpdates,
    unstable_renderSubtreeIntoContainer,
    version,
    // @ts-ignore
    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,
} from 'react-dom'
export default ReactDOM
export {
    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,
    createPortal,
    createRoot,
    findDOMNode,
    flushSync,
    hydrate,
    hydrateRoot,
    render,
    unmountComponentAtNode,
    unstable_batchedUpdates,
    unstable_renderSubtreeIntoContainer,
    version,
}
