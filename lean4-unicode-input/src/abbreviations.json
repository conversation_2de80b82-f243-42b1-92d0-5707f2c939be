{"{}": "{$CURSOR}", "{}_": "{$CURSOR}_", "{{}}": "⦃$CURSOR⦄", "[]": "[$CURSOR]", "[]_": "[$CURSOR]_", "[[]]": "⟦$CURSOR⟧", "<>": "⟨$CURSOR⟩", "()": "($CURSOR)", "()_": "($CURSOR)_", "([])'": "⟮$CURSOR⟯", "(())": "⸨$CURSOR⸩", "f<>": "‹$CURSOR›", "f<<>>": "«$CURSOR»", "[--]": "⁅$CURSOR⁆", "nnnorm": "‖$CURSOR‖₊", "norm": "‖$CURSOR‖", "floor": "⌊$CURSOR⌋", "ceil": "⌈$CURSOR⌉", "nfloor": "⌊$CURSOR⌋₊", "nceil": "⌈$CURSOR⌉₊", "s[]": "⦋$CURSOR⦌", "simplex": "⦋$CURSOR⦌", "\\": "\\", "a": "α", "b": "β", "c": "χ", "d": "↓", "e": "ε", "g": "γ", "i": "∩", "m": "μ", "n": "¬", "o": "∘", "p": "Π", "t": "▸", "r": "→", "u": "↑", "v": "∨", "x": "×", "-": "⁻¹", "~": "∼", ".": "·", "*": "⋆", "?": "¿", "1": "₁", "2": "₂", "3": "₃", "4": "₄", "5": "₅", "6": "₆", "7": "₇", "8": "₈", "9": "₉", "0": "₀", "l": "←", "<": "⟨", ">": "⟩", "O": "Ø", "&": "⅋", "A": "𝔸", "C": "ℂ", "D": "Δ", "F": "𝔽", "G": "Γ", "H": "ℍ", "I": "⋂", "I0": "⋂₀", "K": "𝕂", "L": "Λ", "N": "ℕ", "P": "Π", "Q": "ℚ", "R": "ℝ", "S": "Σ", "U": "⋃", "U0": "⋃₀", "Z": "ℤ", "#": "♯", ":": "∶", "|": "∣", "!": "¡", "be": "β", "ga": "γ", "de": "δ", "ep": "ε", "ze": "ζ", "et": "η", "th": "θ", "io": "ι", "ka": "κ", "la": "λ", "mu": "μ", "nu": "ν", "xi": "ξ", "pi": "π", "rh": "ρ", "vsi": "ς", "si": "σ", "ta": "τ", "ph": "φ", "ch": "χ", "ps": "ψ", "om": "ω", "`A": "À", "'A": "Á", "^{A}": "Â", "~A": "Ã", "\"A": "Ä", "cC": "Ç", "`E": "È", "'E": "É", "^{E}": "Ê", "\"E": "Ë", "`I": "Ì", "'I": "Í", "^{I}": "Î", "\"I": "Ï", "~N": "Ñ", "`O": "Ò", "'O": "<PERSON>", "^{O}": "Ô", "~O": "Õ", "\"O": "Ö", "/O": "Ø", "`U": "Ù", "'U": "Ú", "^{U}": "Û", "\"U": "Ü", "'Y": "Ý", "`a": "à", "'a": "á", "^{a}": "â", "~a": "ã", "\"a": "ä", "cc": "ç", "`e": "è", "'e": "é", "^{e}": "ê", "\"e": "ë", "`i": "ì", "'i": "í", "^{i}": "î", "\"i": "ï", "~{n}": "ñ", "`o": "ò", "'o": "ó", "^{o}": "ô", "~o": "õ", "\"o": "ö", "/o": "ø", "`u": "ù", "'u": "ú", "^{u}": "û", "\"u": "ü", "'y": "ý", "\"y": "ÿ", "/L": "Ł", "notin": "∉", "note": "♩", "not": "¬", "nomisma": "𐆎", "nin": "∉", "nni": "∌", "ni": "∋", "nattrans": "⟹", "nat_trans": "⟹", "natural": "♮", "nat": "ℕ", "naira": "₦", "nabla": "∇", "napprox": "≉", "numero": "№", "nLeftarrow": "⇍", "nLeftrightarrow": "⇎", "nRightarrow": "⇏", "nVDash": "⊯", "nVdash": "⊮", "ncong": "≇", "nearrow": "↗", "neg": "¬", "nequiv": "≢", "neq": "≠", "nexists": "∄", "ne": "≠", "ngeqq": "≱", "ngeqslant": "≱", "ngeq": "≱", "ngtr": "≯", "nleftarrow": "↚", "nleftrightarrow": "↮", "nleqq": "≰", "nleqslant": "≰", "nleq": "≰", "nless": "≮", "nmid": "∤", "nparallel": "∦", "npreceq": "⋠", "nprec": "⊀", "nrightarrow": "↛", "nshortmid": "∤", "nsimeq": "≄", "nsim": "≁", "nsubseteqq": "⊈", "nsubseteq": "⊈", "nsubset": "⊄", "nsucceq": "⋡", "nsucc": "⊁", "nsupseteqq": "⊉", "nsupseteq": "⊉", "nsupset": "⊅", "ntrianglelefteq": "⋬", "ntriangleleft": "⋪", "ntrianglerighteq": "⋭", "ntriangleright": "⋫", "nvDash": "⊭", "nvdash": "⊬", "nwarrow": "↖", "eqn": "≠", "equiv": "≃", "eqcirc": "≖", "eqcolon": "≕", "eqslantgtr": "⋝", "eqslantless": "⋜", "entails": "⊢", "en": "–", "exn": "∄", "exists": "∃", "ex": "∃", "emptyset": "∅", "empty": "∅", "em": "—", "epsilon": "ε", "eps": "ε", "euro": "€", "eta": "η", "ell": "ℓ", "iso": "≅", "in": "∈", "inn": "∉", "inter": "∩", "intercal": "⊺", "intersection": "∩", "integral": "∫", "integral-": "⨍", "int": "ℤ", "inv": "⁻¹", "increment": "∆", "inf": "⊓", "infi": "⨅", "infty": "∞", "iff": "↔", "imp": "→", "imath": "ı", "iota": "ι", "=n": "≠", "==n": "≢", "===": "≣", "==>": "⟹", "==": "≡", "=:": "≕", "=o": "≗", "=>n": "⇏", "=>": "⇒", "~n": "≁", "~~n": "≉", "~~~": "≋", "~~-": "≊", "~~": "≈", "~-n": "≄", "~-": "≃", "~=n": "≇", "~=": "≅", "homotopy": "∼", "hom": "⟶", "hori": "ϩ", "hookleftarrow": "↩", "hookrightarrow": "↪", "hryvnia": "₴", "heta": "ͱ", "heartsuit": "♥", "hbar": "ℏ", ":~": "∻", ":=": "≔", "::-": "∺", "::": "∷", "-~": "≂", "-|": "⊣", "-1": "⁻¹", "^-1": "⁻¹", "-2": "⁻²", "-3": "⁻³", "-:": "∹", "->n": "↛", "->": "→", "-->": "⟶", "---": "─", "--=": "═", "--_": "━", "--.": "╌", "-o": "⊸", ".=.": "≑", ".=": "≐", ".+": "∔", ".-": "∸", "...": "⋯", "(=": "≘", "(b": "⟅", "and=": "≙", "and": "∧", "an": "∧", "angle": "∠", "rightangle": "∟", "angstrom": "Å", "all": "∀", "allf": "∀ᶠ", "all^f": "∀ᶠ", "allm": "∀ᵐ", "all^m": "∀ᵐ", "alpha": "α", "aleph": "ℵ", "aleph0": "ℵ₀", "asterisk": "⁎", "ast": "∗", "asymp": "≍", "apl": "⌶", "approxeq": "≊", "approx": "≈", "aa": "å", "ae": "æ", "austral": "₳", "afghani": "؋", "amalg": "∐", "average": "⨍", "-int": "⨍", "or=": "≚", "ordfeminine": "ª", "ordmasculine": "º", "or": "∨", "oplus": "⊕", "od": "ᵒᵈ", "orderdual": "ᵒᵈ", "addopposite": "ᵃᵒᵖ", "aop": "ᵃᵒᵖ", "mulopposite": "ᵐᵒᵖ", "mop": "ᵐᵒᵖ", "opposite": "ᵒᵖ", "op": "ᵒᵖ", "o+": "⊕", "o--": "⊖", "o-": "⊝", "ox": "⊗", "o/": "⊘", "o.": "⊙", "oo": "⊚", "o*": "∘*", "o=": "⊜", "oe": "œ", "octagonal": "🛑", "ohm": "Ω", "ounce": "℥", "omega": "ω", "omicron": "ο", "ominus": "⊖", "odot": "⊙", "oint": "∮", "oiint": "∯", "oslash": "⊘", "otimes": "⊗", "tensorproduct": "⊗", "pitensorproduct": "⨂", "tensorpower": "⨂", "pd": "∂", "*=": "≛", "t=": "≜", "tint": "∯", "transport": "▹", "trans": "▹", "triangledown": "▿", "trianglelefteq": "⊴", "triangleleft": "◃", "triangleq": "≜", "trianglerighteq": "⊵", "triangleright": "▹", "triangle": "▵", "tr": "⬝", "tb": "◂", "twoheadleftarrow": "↞", "twoheadrightarrow": "↠", "tw": "◃", "tie": "⁀", "times": "×", "theta": "θ", "therefore": "∴", "thickapprox": "≈", "thicksim": "∼", "telephone": "℡", "tenge": "₸", "textmusicalnote": "♪", "textmu": "µ", "textfractionsolidus": "⁄", "textbaht": "฿", "textdied": "✝", "textdiscount": "⁒", "textcolonmonetary": "₡", "textcircledP": "℗", "textwon": "₩", "textnaira": "₦", "textnumero": "№", "textpeso": "₱", "textpertenthousand": "‱", "textlira": "₤", "textlquill": "⁅", "textrecipe": "℞", "textreferencemark": "※", "textrquill": "⁆", "textinterrobang": "‽", "textestimated": "℮", "textopenbullet": "◦", "tugrik": "₮", "tau": "τ", "top": "⊤", "to": "→", "to0": "→₀", "r0": "→₀", "to_0": "→₀", "r_0": "→₀", "finsupp": "→₀", "to1": "→₁", "r1": "→₁", "to_1": "→₁", "r_1": "→₁", "l1": "→₁", "to1s": "→₁ₛ", "r1s": "→₁ₛ", "to_1s": "→₁ₛ", "r_1s": "→₁ₛ", "l1simplefunc": "→₁ₛ", "toa": "→ₐ", "ra": "→ₐ", "to_a": "→ₐ", "r_a": "→ₐ", "alghom": "→ₐ", "tob": "→ᵇ", "rb": "→ᵇ", "to^b": "→ᵇ", "r^b": "→ᵇ", "boundedcontinuousfunction": "→ᵇ", "tol": "→ₗ", "rl": "→ₗ", "to_l": "→ₗ", "r_l": "→ₗ", "linearmap": "→ₗ", "tom": "→ₘ", "rm": "→ₘ", "to_m": "→ₘ", "r_m": "→ₘ", "aeeqfun": "→ₘ", "rp": "→ₚ", "to_p": "→ₚ", "r_p": "→ₚ", "dfinsupp": "→ₚ", "tos": "→ₛ", "rs": "→ₛ", "to_s": "→ₛ", "r_s": "→ₛ", "simplefunc": "→ₛ", "heyting": "⇨", "himp": "⇨", "hnot": "￢", "covers": "⋖", "covby": "⋖", "wcovby": "⩿", "wcovers": "⩿", "def=": "≝", "defs": "≙", "degree": "°", "dei": "ϯ", "delta": "δ", "doteqdot": "≑", "doteq": "≐", "dotplus": "∔", "dotsquare": "⊡", "dot": "⬝", "dong": "₫", "downarrow": "↓", "downdownarrows": "⇊", "downleftharpoon": "⇃", "downrightharpoon": "⇂", "dr-": "↘", "dr=": "⇘", "drachma": "₯", "dr": "↘", "dl-": "↙", "dl=": "⇙", "dl": "↙", "d-2": "⇊", "d-u-": "⇵", "d-|": "↧", "d-": "↓", "d==": "⟱", "d=": "⇓", "dd-": "↡", "ddagger": "‡", "ddag": "‡", "ddots": "⋱", "dz": "↯", "dib": "◆", "diw": "◇", "di.": "◈", "die": "⚀", "division": "÷", "divideontimes": "⋇", "div": "÷", "diameter": "⌀", "diamondsuit": "♢", "diamond": "⋄", "digamma": "ϝ", "di": "◆", "dagger": "†", "dag": "†", "daleth": "ℸ", "dashv": "⊣", "dh": "ð", "dvd": "∣", "m=": "≞", "meet": "⊓", "member": "∈", "mem": "∈", "measuredangle": "∡", "mapsto": "↦", "male": "♂", "maltese": "✠", "manat": "₼", "mathscr{I}": "ℐ", "minus": "−", "mill": "₥", "micro": "µ", "mid": "∣", "multiplication": "×", "multimap": "⊸", "mho": "℧", "models": "⊧", "mp": "∓", "?=": "≟", "??": "⁇", "?!": "‽", "prohibited": "🛇", "prod": "∏", "propto": "∝", "precapprox": "≾", "preceq": "≼", "precnapprox": "⋨", "precnsim": "⋨", "precsim": "≾", "prec": "≺", "preim": "⁻¹'", "preimage": "⁻¹'", "prime": "′", "pr": "↣", "powerset": "𝒫", "pounds": "£", "pound": "£", "pab": "▰", "paw": "▱", "partnership": "㉐", "partial": "∂", "paragraph": "¶", "parallel": "∥", "pa": "▰", "pm": "±", "perp": "⟂", "^perp": "ᗮ", "permil": "‰", "per": "⅌", "peso": "₱", "peseta": "₧", "pilcrow": "¶", "pitchfork": "⋔", "psi": "ψ", "phi": "φ", "leqn": "≰", "leqq": "≦", "leqslant": "≤", "leq": "≤", "len": "≰", "leadsto": "↝", "leftarrowtail": "↢", "leftarrow": "←", "leftharpoondown": "↽", "leftharpoonup": "↼", "leftleftarrows": "⇇", "leftrightarrows": "⇆", "leftrightarrow": "↔", "leftrightharpoons": "⇋", "leftrightsquigarrow": "↭", "leftthreetimes": "⋋", "lessapprox": "≲", "lessdot": "⋖", "lesseqgtr": "⋚", "lesseqqgtr": "⋚", "lessgtr": "≶", "lesssim": "≲", "le": "≤", "lub": "⊔", "lr--": "⟷", "lr-n": "↮", "lr-": "↔", "lr=n": "⇎", "lr=": "⇔", "lr~": "↭", "lrcorner": "⌟", "lr": "↔", "l-2": "⇇", "l-r-": "⇆", "l--": "⟵", "l-n": "↚", "l-|": "↤", "l->": "↢", "l-": "←", "l==": "⇚", "l=n": "⇍", "l=": "⇐", "l~": "↜", "ll-": "↞", "llcorner": "⌞", "llbracket": "〚", "ll": "≪", "lbag": "⟅", "lambda": "λ", "lamda": "λ", "lam": "λ", "lari": "₾", "langle": "⟨", "lira": "₤", "lceil": "⌈", "ldots": "…", "ldq": "“", "ldata": "《", "lfloor": "⌊", "lf": "⧏", "<|": "⧏", "lhd": "◁", "lnapprox": "⋦", "lneqq": "≨", "lneq": "≨", "lnsim": "⋦", "lnot": "¬", "longleftarrow": "⟵", "longleftrightarrow": "⟷", "longrightarrow": "⟶", "looparrowleft": "↫", "looparrowright": "↬", "lozenge": "✧", "lq": "‘", "ltimes": "⋉", "lvertneqq": "≨", "geqn": "≱", "geqq": "≧", "geqslant": "≥", "geq": "≥", "gen": "≱", "gets": "←", "ge": "≥", "glb": "⊓", "glqq": "„", "glq": "‚", "guarani": "₲", "gangia": "ϫ", "gamma": "γ", "ggg": "⋙", "gg": "≫", "gimel": "ℷ", "gnapprox": "⋧", "gneqq": "≩", "gneq": "≩", "gnsim": "⋧", "gtrapprox": "≳", "gtrdot": "⋗", "gtreqless": "⋛", "gtreqqless": "⋛", "gtrless": "≷", "gtrsim": "≳", "gvertneqq": "≩", "grqq": "“", "grq": "‘", "<=n": "≰", "<=>n": "⇎", "<=>": "⇔", "<=": "≤", "<n": "≮", "<~nn": "≴", "<~n": "⋦", "<~": "≲", "<:": "⋖", ":>": "⋗", "<->n": "↮", "<->": "↔", "<-->": "⟷", "<--": "⟵", "<-n": "↚", "<-": "←", "<<": "⟪", ">=n": "≱", ">=": "≥", ">n": "≯", ">~nn": "≵", ">~n": "⋧", ">~": "≳", ">>": "⟫", "root": "√", "scissor": "✂", "ssubn": "⊄", "ssub": "⊂", "ssupn": "⊅", "ssup": "⊃", "ssqub": "⊏", "ssqup": "⊐", "ss": "⊆", "subn": "⊈", "subseteqq": "⊆", "subseteq": "⊆", "subsetneqq": "⊊", "subsetneq": "⊊", "subset": "⊆", "ssubset": "⊂", "sub": "⊆", "supn": "⊉", "supseteqq": "⊇", "supseteq": "⊇", "supsetneqq": "⊋", "supsetneq": "⊋", "supset": "⊇", "ssupset": "⊃", "sUnion": "⋃₀", "sInter": "⋂₀", "sup": "⊔", "supr": "⨆", "surd3": "∛", "surd4": "∜", "surd": "√", "succapprox": "≿", "succcurlyeq": "≽", "succeq": "≽", "succnapprox": "⋩", "succnsim": "⋩", "succsim": "≿", "succ": "≻", "sum": "∑", "specializes": "⤳", "~>": "⤳", "squbn": "⋢", "squb": "⊑", "squpn": "⋣", "squp": "⊒", "square": "□", "squigarrowright": "⇝", "sqb": "■", "sqw": "□", "sq.": "▣", "sqo": "▢", "sqcap": "⊓", "sqcup": "⊔", "sqrt": "√", "sqsubseteq": "⊑", "sqsubset": "⊏", "sqsupseteq": "⊒", "sqsupset": "⊐", "sq": "◾", "sy": "⁻¹", "symmdiff": "∆", "st4": "✦", "st6": "✶", "st8": "✴", "st12": "✹", "stigma": "ϛ", "star": "⋆", "straightphi": "φ", "st": "⋆", "spesmilo": "₷", "span": "∙", "spadesuit": "♠", "sphericalangle": "∢", "section": "§", "searrow": "↘", "setminus": "\\", "san": "ϻ", "sampi": "ϡ", "shortmid": "∣", "sho": "ϸ", "shima": "ϭ", "shei": "ϣ", "sharp": "♯", "sigma": "σ", "simeq": "≃", "sim": "∼", "sbs": "﹨", "smallamalg": "∐", "smallsetminus": "∖", "smallsmile": "⌣", "smile": "⌣", "smul": "•", "swarrow": "↙", "Tr": "◀", "Tb": "◀", "Tw": "◁", "Tau": "Τ", "Theta": "Θ", "TH": "Þ", "union": "∪", "undertie": "‿", "uncertainty": "⯑", "un": "∪", "u+": "⊎", "u.": "⊍", "ud-|": "↨", "ud-": "↕", "ud=": "⇕", "ud": "↕", "ul-": "↖", "ul=": "⇖", "ulcorner": "⌜", "ul": "↖", "ur-": "↗", "ur=": "⇗", "urcorner": "⌝", "ur": "↗", "u-2": "⇈", "u-d-": "⇅", "u-|": "↥", "u-": "↑", "u==": "⟰", "u=": "⇑", "uu-": "↟", "upsilon": "υ", "uparrow": "↑", "updownarrow": "↕", "upleftharpoon": "↿", "uplus": "⊎", "uprightharpoon": "↾", "upuparrows": "⇈", "And": "⋀", "AA": "Å", "AE": "<PERSON>", "Alpha": "Α", "Or": "⋁", "O+": "⨁", "directsum": "⨁", "Ox": "⨂", "O.": "⨀", "O*": "⍟", "OE": "Œ", "Omega": "Ω", "Omicron": "Ο", "Int": "ℤ", "Inter": "⋂", "bInter": "⋂", "Iota": "Ι", "Im": "ℑ", "Un": "⋃", "Union": "⋃", "bUnion": "⋃", "U+": "⨄", "U.": "⨃", "Upsilon": "Υ", "Uparrow": "⇑", "Updownarrow": "⇕", "Gl-": "ƛ", "Gl": "λ", "Gangia": "Ϫ", "Gamma": "Γ", "Glb": "⨅", "Ga": "α", "GA": "Α", "Gb": "β", "GB": "Β", "Gg": "γ", "GG": "Γ", "Gd": "δ", "GD": "Δ", "Ge": "ε", "GE": "Ε", "Gz": "ζ", "GZ": "Ζ", "Gth": "θ", "Gt": "τ", "GTH": "Θ", "GT": "Τ", "Gi": "ι", "GI": "Ι", "Gk": "κ", "GK": "Κ", "GL": "Λ", "Gm": "μ", "GM": "Μ", "Gn": "ν", "GN": "Ν", "Gx": "ξ", "GX": "Ξ", "Gr": "ρ", "GR": "Ρ", "Gs": "σ", "GS": "Σ", "Gu": "υ", "GU": "Υ", "Gf": "φ", "GF": "Φ", "Gc": "χ", "GC": "Χ", "Gp": "ψ", "GP": "Ψ", "Go": "ω", "GO": "Ω", "Inf": "⨅", "Join": "⨆", "Lub": "⨆", "Lambda": "Λ", "Lamda": "Λ", "Leftarrow": "⇐", "Leftrightarrow": "⇔", "Letter": "✉", "Lleftarrow": "⇚", "Ll": "⋘", "Longleftarrow": "⇐", "Longleftrightarrow": "⇔", "Longrightarrow": "⇒", "Meet": "⨅", "Sup": "⨆", "Sqcap": "⨅", "Sqcup": "⨆", "Lsh": "↰", "|-n": "⊬", "|-": "⊢", "|=n": "⊭", "|=": "⊨", "|->": "↦", "|=>": "⇰", "||-n": "⊮", "||-": "⊩", "||=n": "⊯", "||=": "⊫", "|||-": "⊪", "||": "‖", "fuzzy": "‖", "|n": "∤", "Com": "ℂ", "Chi": "Χ", "Cap": "⋒", "Cup": "⋓", "cul": "⌜", "cuL": "⌈", "currency": "¤", "curlyeqprec": "⋞", "curlyeqsucc": "⋟", "curlypreceq": "≼", "curlyvee": "⋎", "curlywedge": "⋏", "curvearrowleft": "↶", "curvearrowright": "↷", "cur": "⌝", "cuR": "⌉", "cup": "∪", "cu": "⌜", "cll": "⌞", "clL": "⌊", "clr": "⌟", "clR": "⌋", "clubsuit": "♣", "cl": "⌞", "construction": "🚧", "cong": "≅", "con": "⬝", "compl": "ᶜ", "complement": "ᶜ", "complementprefix": "∁", "Complement": "∁", "comp": "∘", "com": "ℂ", "coloneq": "≔", "colon": "₡", "copyright": "©", "cdots": "⋯", "cdot": "⬝", "cib": "●", "ciw": "○", "ci..": "◌", "ci.": "◎", "ciO": "◯", "circeq": "≗", "circlearrowleft": "↺", "circlearrowright": "↻", "circledR": "®", "circledS": "Ⓢ", "circledast": "⊛", "circledcirc": "⊚", "circleddash": "⊝", "circ": "∘", "ci": "●", "centerdot": "·", "cent": "¢", "cedi": "₵", "celsius": "℃", "ce": "ȩ", "checkmark": "✓", "chi": "χ", "cruzeiro": "₢", "caution": "☡", "cap": "∩", "qed": "∎", "quad": " ", "quot": "⧸", "bigsolidus": "⧸", "/": "⧸", "+ ": "⊹", "b+": "⊞", "b-": "⊟", "bx": "⊠", "b.": "⊡", "bn": "ℕ", "bz": "ℤ", "bq": "ℚ", "brokenbar": "¦", "br": "ℝ", "bc": "ℂ", "bp": "ℙ", "bb": "𝔹", "bsum": "⅀", "b0": "𝟘", "b1": "𝟙", "b2": "𝟚", "b3": "𝟛", "b4": "𝟜", "b5": "𝟝", "b6": "𝟞", "b7": "𝟟", "b8": "𝟠", "b9": "𝟡", "sb0": "𝟬", "sb1": "𝟭", "sb2": "𝟮", "sb3": "𝟯", "sb4": "𝟰", "sb5": "𝟱", "sb6": "𝟲", "sb7": "𝟳", "sb8": "𝟴", "sb9": "𝟵", "bub": "•", "buw": "◦", "but": "‣", "bumpeq": "≏", "bu": "•", "biohazard": "☣", "bihimp": "⇔", "bigcap": "⋂", "bigcirc": "◯", "bigcoprod": "∐", "bigcup": "⋃", "bigglb": "⨅", "biginf": "⨅", "bigjoin": "⨆", "biglub": "⨆", "bigmeet": "⨅", "bigsqcap": "⨅", "bigsqcup": "⨆", "bigstar": "★", "bigsup": "⨆", "bigtriangledown": "▽", "bigtriangleup": "△", "bigvee": "⋁", "bigwedge": "⋀", "beta": "β", "beth": "ℶ", "between": "≬", "because": "∵", "backcong": "≌", "backepsilon": "∍", "backprime": "‵", "backsimeq": "⋍", "backsim": "∽", "barwedge": "⊼", "blacklozenge": "✦", "blacksquare": "▪", "blacksmiley": "☻", "blacktriangledown": "▾", "blacktriangleleft": "◂", "blacktriangleright": "▸", "blacktriangle": "▴", "bot": "⊥", "^bot": "ᗮ", "bowtie": "⋈", "boxminus": "⊟", "boxmid": "◫", "hcomp": "◫", "boxplus": "⊞", "boxtimes": "⊠", "join": "⊔", "r-2": "⇉", "r-3": "⇶", "r-l-": "⇄", "r--": "⟶", "r-n": "↛", "r-|": "↦", "r->": "↣", "r-o": "⊸", "r-": "→", "r==": "⇛", "r=n": "⇏", "r=": "⇒", "r~": "↝", "rr-": "↠", "reb": "▬", "rew": "▭", "real": "ℝ", "registered": "®", "re": "▬", "rbag": "⟆", "rat": "ℚ", "radioactive": "☢", "rrbracket": "〛", "rangle": "⟩", "rq": "’", "rightarrowtail": "↣", "rightarrow": "→", "rightharpoondown": "⇁", "rightharpoonup": "⇀", "rightleftarrows": "⇄", "rightleftharpoons": "⇌", "rightrightarrows": "⇉", "rightthreetimes": "⋌", "risingdotseq": "≓", "ruble": "₽", "rupee": "₨", "rho": "ρ", "rhd": "▷", "rceil": "⌉", "rfloor": "⌋", "rtimes": "⋊", "rdq": "”", "rdata": "》", "functor": "⥤", "fun": "λ", "f<<": "«", "f>>": "»", "f<": "‹", "f>": "›", "finprod": "∏ᶠ", "finsum": "∑ᶠ", "frac12": "½", "frac13": "⅓", "frac14": "¼", "frac15": "⅕", "frac16": "⅙", "frac18": "⅛", "frac1": "⅟", "frac23": "⅔", "frac25": "⅖", "frac34": "¾", "frac35": "⅗", "frac38": "⅜", "frac45": "⅘", "frac56": "⅚", "frac58": "⅝", "frac78": "⅞", "frac": "¼", "frown": "⌢", "frqq": "»", "frq": "›", "female": "♀", "fei": "ϥ", "facsimile": "℻", "fallingdotseq": "≒", "flat": "♭", "flqq": "«", "flq": "‹", "forall": "∀", ")b": "⟆", "[[": "⟦", "]]": "⟧", "{{": "⦃", "}}": "⦄", "((": "⸨", "))": "⸩", "([": "⟮", "])": "⟯", "Xi": "Ξ", "Nat": "ℕ", "Nu": "Ν", "Zeta": "Ζ", "Rat": "ℚ", "Real": "ℝ", "Re": "ℜ", "Rho": "Ρ", "Rightarrow": "⇒", "Rrightarrow": "⇛", "Rsh": "↱", "Fei": "Ϥ", "Frowny": "☹", "Hori": "Ϩ", "Heta": "Ͱ", "Khei": "Ϧ", "Koppa": "Ϟ", "Kappa": "Κ", "^a": "ᵃ", "^b": "ᵇ", "^c": "ᶜ", "^d": "ᵈ", "^e": "ᵉ", "^f": "ᶠ", "^g": "ᵍ", "^h": "ʰ", "^i": "ⁱ", "^j": "ʲ", "^k": "ᵏ", "^l": "ˡ", "^m": "ᵐ", "^n": "ⁿ", "^o": "ᵒ", "^p": "ᵖ", "^r": "ʳ", "^s": "ˢ", "^t": "ᵗ", "^u": "ᵘ", "^v": "ᵛ", "^w": "ʷ", "^x": "ˣ", "^y": "ʸ", "^z": "ᶻ", "^A": "ᴬ", "^B": "ᴮ", "^D": "ᴰ", "^E": "ᴱ", "^G": "ᴳ", "^H": "ᴴ", "^I": "ᴵ", "^J": "ᴶ", "^K": "ᴷ", "^L": "ᴸ", "^M": "ᴹ", "^N": "ᴺ", "^O": "ᴼ", "^P": "ᴾ", "^R": "ᴿ", "^T": "ᵀ", "^U": "ᵁ", "^V": "ⱽ", "^W": "ᵂ", "^0": "⁰", "^1": "¹", "^2": "²", "^3": "³", "^4": "⁴", "^5": "⁵", "^6": "⁶", "^7": "⁷", "^8": "⁸", "^9": "⁹", "^)": "⁾", "^(": "⁽", "^=": "⁼", "^+": "⁺", "^o_": "º", "^-": "⁻", "^a_": "ª", "^uhook": "ꭟ", "^ubar": "ᶶ", "^upsilon": "ᶷ", "^ltilde": "ꭞ", "^ls": "ꭝ", "^lhook": "ᶪ", "^lretroflexhook": "ᶩ", "^oe": "ꟹ", "^heng": "ꭜ", "^hhook": "ʱ", "^hwithhook": "ʱ", "^Hstroke": "ꟸ", "^theta": "ᶿ", "^turnedv": "ᶺ", "^turnedmleg": "ᶭ", "^turnedm": "ᵚ", "^turnedh": "ᶣ", "^turnedalpha": "ᶛ", "^turnedae": "ᵆ", "^turneda": "ᵄ", "^turnedi": "ᵎ", "^turnede": "ᵌ", "^turnedrhook": "ʵ", "^turnedrwithhook": "ʵ", "^turnedr": "ʴ", "^twithpalatalhook": "ᶵ", "^otop": "ᵔ", "^ezh": "ᶾ", "^esh": "ᶴ", "^eth": "ᶞ", "^eng": "ᵑ", "^zcurl": "ᶽ", "^zretroflexhook": "ᶼ", "^vhook": "ᶹ", "^Ismall": "ᶦ", "^Lsmall": "ᶫ", "^Nsmall": "ᶰ", "^Usmall": "ᶸ", "^Istroke": "ᶧ", "^Rinverted": "ʶ", "^ccurl": "ᶝ", "^chi": "ᵡ", "^shook": "ᶳ", "^gscript": "ᶢ", "^schwa": "ᵊ", "^usideways": "ᵙ", "^phi": "ᶲ", "^obarred": "ᶱ", "^beta": "ᵝ", "^obottom": "ᵕ", "^nretroflexhook": "ᶯ", "^nlefthook": "ᶮ", "^mhook": "ᶬ", "^jtail": "ᶨ", "^iota": "ᶥ", "^istroke": "ᶤ", "^ereversedopen": "ᶟ", "^stop": "ˤ", "^varphi": "ᵠ", "^vargamma": "ᵞ", "^gamma": "ˠ", "^ain": "ᵜ", "^alpha": "ᵅ", "^oopen": "ᵓ", "^eopen": "ᵋ", "^Ou": "ᴽ", "^Nreversed": "ᴻ", "^Ereversed": "ᴲ", "^Bbarred": "ᴯ", "^Ae": "ᴭ", "^SM": "℠", "^TEL": "℡", "^TM": "™", "_a": "ₐ", "_e": "ₑ", "_h": "ₕ", "_i": "ᵢ", "_j": "ⱼ", "_k": "ₖ", "_l": "ₗ", "_m": "ₘ", "_n": "ₙ", "_o": "ₒ", "_p": "ₚ", "_r": "ᵣ", "_s": "ₛ", "_t": "ₜ", "_u": "ᵤ", "_v": "ᵥ", "_x": "ₓ", "_0": "₀", "_1": "₁", "_2": "₂", "_3": "₃", "_4": "₄", "_5": "₅", "_6": "₆", "_7": "₇", "_8": "₈", "_9": "₉", "_)": "₎", "_(": "₍", "_=": "₌", "_+": "₊", "_--": "̲", "_-": "₋", "!!": "‼", "!?": "⁉", "San": "Ϻ", "Sampi": "Ϡ", "Sho": "Ϸ", "Shima": "Ϭ", "Shei": "Ϣ", "Stigma": "Ϛ", "Sigma": "Σ", "Subset": "⋐", "Supset": "⋑", "Smiley": "☺", "Psi": "Ψ", "Phi": "Φ", "Pi": "Π", "Pi0": "Π₀", "P0": "Π₀", "Pi_0": "Π₀", "P_0": "Π₀", "bfA": "𝐀", "bfB": "𝐁", "bfC": "𝐂", "bfD": "𝐃", "bfE": "𝐄", "bfF": "𝐅", "bfG": "𝐆", "bfH": "𝐇", "bfI": "𝐈", "bfJ": "𝐉", "bfK": "𝐊", "bfL": "𝐋", "bfM": "𝐌", "bfN": "𝐍", "bfO": "𝐎", "bfP": "𝐏", "bfQ": "𝐐", "bfR": "𝐑", "bfS": "𝐒", "bfT": "𝐓", "bfU": "𝐔", "bfV": "𝐕", "bfW": "𝐖", "bfX": "𝐗", "bfY": "𝐘", "bfZ": "𝐙", "bfa": "𝐚", "bfb": "𝐛", "bfc": "𝐜", "bfd": "𝐝", "bfe": "𝐞", "bff": "𝐟", "bfg": "𝐠", "bfh": "𝐡", "bfi": "𝐢", "bfj": "𝐣", "bfk": "𝐤", "bfl": "𝐥", "bfm": "𝐦", "bfn": "𝐧", "bfo": "𝐨", "bfp": "𝐩", "bfq": "𝐪", "bfr": "𝐫", "bfs": "𝐬", "bft": "𝐭", "bfu": "𝐮", "bfv": "𝐯", "bfw": "𝐰", "bfx": "𝐱", "bfy": "𝐲", "bfz": "𝐳", "MiA": "𝐴", "MiB": "𝐵", "MiC": "𝐶", "MiD": "𝐷", "MiE": "𝐸", "MiF": "𝐹", "MiG": "𝐺", "MiH": "𝐻", "MiI": "𝐼", "MiJ": "𝐽", "MiK": "𝐾", "MiL": "𝐿", "MiM": "𝑀", "MiN": "𝑁", "MiO": "𝑂", "MiP": "𝑃", "MiQ": "𝑄", "MiR": "𝑅", "MiS": "𝑆", "MiT": "𝑇", "MiU": "𝑈", "MiV": "𝑉", "MiW": "𝑊", "MiX": "𝑋", "MiY": "𝑌", "MiZ": "𝑍", "Mia": "𝑎", "Mib": "𝑏", "Mic": "𝑐", "Mid": "𝑑", "Mie": "𝑒", "Mif": "𝑓", "Mig": "𝑔", "Mii": "𝑖", "Mij": "𝑗", "Mik": "𝑘", "Mil": "𝑙", "Mim": "𝑚", "Min": "𝑛", "Mio": "𝑜", "Mip": "𝑝", "Miq": "𝑞", "Mir": "𝑟", "Mis": "𝑠", "Mit": "𝑡", "Miu": "𝑢", "Miv": "𝑣", "Miw": "𝑤", "Mix": "𝑥", "Miy": "𝑦", "Miz": "𝑧", "MIA": "𝑨", "MIB": "𝑩", "MIC": "𝑪", "MID": "𝑫", "MIE": "𝑬", "MIF": "𝑭", "MIG": "𝑮", "MIH": "𝑯", "MII": "𝑰", "MIJ": "𝑱", "MIK": "𝑲", "MIL": "𝑳", "MIM": "𝑴", "MIN": "𝑵", "MIO": "𝑶", "MIP": "𝑷", "MIQ": "𝑸", "MIR": "𝑹", "MIS": "𝑺", "MIT": "𝑻", "MIU": "𝑼", "MIV": "𝑽", "MIW": "𝑾", "MIX": "𝑿", "MIY": "𝒀", "MIZ": "𝒁", "MIa": "𝒂", "MIb": "𝒃", "MIc": "𝒄", "MId": "𝒅", "MIe": "𝒆", "MIf": "𝒇", "MIg": "𝒈", "MIh": "𝒉", "MIi": "𝒊", "MIj": "𝒋", "MIk": "𝒌", "MIl": "𝒍", "MIm": "𝒎", "MIn": "𝒏", "MIo": "𝒐", "MIp": "𝒑", "MIq": "𝒒", "MIr": "𝒓", "MIs": "𝒔", "MIt": "𝒕", "MIu": "𝒖", "MIv": "𝒗", "MIw": "𝒘", "MIx": "𝒙", "MIy": "𝒚", "MIz": "𝒛", "McA": "𝒜", "McB": "ℬ", "McC": "𝒞", "McD": "𝒟", "McE": "ℰ", "McF": "ℱ", "McG": "𝒢", "McH": "ℋ", "McI": "ℐ", "McJ": "𝒥", "McK": "𝒦", "McL": "ℒ", "McM": "ℳ", "McN": "𝒩", "McO": "𝒪", "McP": "𝒫", "McQ": "𝒬", "McR": "ℛ", "McS": "𝒮", "McT": "𝒯", "McU": "𝒰", "McV": "𝒱", "McW": "𝒲", "McX": "𝒳", "McY": "𝒴", "McZ": "𝒵", "Mca": "𝒶", "Mcb": "𝒷", "Mcc": "𝒸", "Mcd": "𝒹", "Mce": "ℯ", "Mcf": "𝒻", "Mcg": "ℊ", "Mch": "𝒽", "Mci": "𝒾", "Mcj": "𝒿", "Mck": "𝓀", "Mcl": "𝓁", "Mcm": "𝓂", "Mcn": "𝓃", "Mco": "ℴ", "Mcp": "𝓅", "Mcq": "𝓆", "Mcr": "𝓇", "Mcs": "𝓈", "Mct": "𝓉", "Mcu": "𝓊", "Mcv": "𝓋", "Mcw": "𝓌", "Mcx": "𝓍", "Mcy": "𝓎", "Mcz": "𝓏", "MCA": "𝓐", "MCB": "𝓑", "MCC": "𝓒", "MCD": "𝓓", "MCE": "𝓔", "MCF": "𝓕", "MCG": "𝓖", "MCH": "𝓗", "MCI": "𝓘", "MCJ": "𝓙", "MCK": "𝓚", "MCL": "𝓛", "MCM": "𝓜", "MCN": "𝓝", "MCO": "𝓞", "MCP": "𝓟", "MCQ": "𝓠", "MCR": "𝓡", "MCS": "𝓢", "MCT": "𝓣", "MCU": "𝓤", "MCV": "𝓥", "MCW": "𝓦", "MCX": "𝓧", "MCY": "𝓨", "MCZ": "𝓩", "MCa": "𝓪", "MCb": "𝓫", "MCc": "𝓬", "MCd": "𝓭", "MCe": "𝓮", "MCf": "𝓯", "MCg": "𝓰", "MCh": "𝓱", "MCi": "𝓲", "MCj": "𝓳", "MCk": "𝓴", "MCl": "𝓵", "MCm": "𝓶", "MCn": "𝓷", "MCo": "𝓸", "MCp": "𝓹", "MCq": "𝓺", "MCr": "𝓻", "MCs": "𝓼", "MCt": "𝓽", "MCu": "𝓾", "MCv": "𝓿", "MCw": "𝔀", "MCx": "𝔁", "MCy": "𝔂", "MCz": "𝔃", "MfA": "𝔄", "MfB": "𝔅", "MfC": "ℭ", "MfD": "𝔇", "MfE": "𝔈", "MfF": "𝔉", "MfG": "𝔊", "MfH": "ℌ", "MfI": "ℑ", "MfJ": "𝔍", "MfK": "𝔎", "MfL": "𝔏", "MfM": "𝔐", "MfN": "𝔑", "MfO": "𝔒", "MfP": "𝔓", "MfQ": "𝔔", "MfR": "ℜ", "MfS": "𝔖", "MfT": "𝔗", "MfU": "𝔘", "MfV": "𝔙", "MfW": "𝔚", "MfX": "𝔛", "MfY": "𝔜", "MfZ": "ℨ", "Mfa": "𝔞", "Mfb": "𝔟", "Mfc": "𝔠", "Mfd": "𝔡", "Mfe": "𝔢", "Mff": "𝔣", "Mfg": "𝔤", "Mfh": "𝔥", "Mfi": "𝔦", "Mfj": "𝔧", "Mfk": "𝔨", "Mfl": "𝔩", "Mfm": "𝔪", "Mfn": "𝔫", "Mfo": "𝔬", "Mfp": "𝔭", "Mfq": "𝔮", "Mfr": "𝔯", "Mfs": "𝔰", "Mft": "𝔱", "Mfu": "𝔲", "Mfv": "𝔳", "Mfw": "𝔴", "Mfx": "𝔵", "Mfy": "𝔶", "Mfz": "𝔷", "yen": "¥", "varrho": "ϱ", "varkappa": "ϰ", "varkai": "ϗ", "varnothing": "∅", "varpi": "ϖ", "varphi": "ϕ", "varprime": "′", "varpropto": "∝", "vartheta": "ϑ", "vartriangleleft": "⊲", "vartriangleright": "⊳", "varbeta": "ϐ", "varsigma": "ς", "veebar": "⊻", "vee": "∨", "ve": "ě", "vE": "Ě", "vdash": "⊢", "vdots": "⋮", "vd": "ď", "vDash": "⊨", "vD": "Ď", "vc": "č", "vC": "Č", "koppa": "ϟ", "kip": "₭", "ki": "į", "kI": "Į", "kelvin": "K", "kappa": "κ", "khei": "ϧ", "warning": "⚠", "won": "₩", "wedge": "∧", "wp": "℘", "wr": "≀", "Dei": "Ϯ", "Delta": "Δ", "Digamma": "Ϝ", "Diamond": "◇", "Downarrow": "⇓", "DH": "Ð", "zeta": "ζ", "Eta": "Η", "Epsilon": "Ε", "Beta": "Β", "Box": "□", "Bumpeq": "≎", "bbA": "𝔸", "bbB": "𝔹", "bbC": "ℂ", "bbD": "𝔻", "bbE": "𝔼", "bbF": "𝔽", "bbG": "𝔾", "bbH": "ℍ", "bbI": "𝕀", "bbJ": "𝕁", "bbK": "𝕂", "bbL": "𝕃", "bbM": "𝕄", "bbN": "ℕ", "bbO": "𝕆", "bbP": "ℙ", "bbQ": "ℚ", "bbR": "ℝ", "bbS": "𝕊", "bbT": "𝕋", "bbU": "𝕌", "bbV": "𝕍", "bbW": "𝕎", "bbX": "𝕏", "bbY": "𝕐", "bbZ": "ℤ", "bba": "𝕒", "bbb": "𝕓", "bbc": "𝕔", "bbd": "𝕕", "bbe": "𝕖", "bbf": "𝕗", "bbg": "𝕘", "bbh": "𝕙", "bbi": "𝕚", "bbj": "𝕛", "bbk": "𝕜", "bbl": "𝕝", "bbm": "𝕞", "bbn": "𝕟", "bbo": "𝕠", "bbp": "𝕡", "bbq": "𝕢", "bbr": "𝕣", "bbs": "𝕤", "bbt": "𝕥", "bbu": "𝕦", "bbv": "𝕧", "bbw": "𝕨", "bbx": "𝕩", "bby": "𝕪", "bbz": "𝕫", "Rge0": "ℝ≥0", "R>=0": "ℝ≥0", "nnreal": "ℝ≥0", "ennreal": "ℝ≥0∞", "enat": "ℕ∞", "Zsqrt": "ℤ√", "zsqrtd": "ℤ√", "liel": "⁅", "bracketl": "⁅", "lier": "⁆", "[-": "⁅", "-]": "⁆", "lsimplex": "⦋", "rsimplex": "⦌", "bracketr": "⁆", "nhds": "𝓝", "nbhds": "𝓝", "X": "⨯", "vectorproduct": "⨯", "crossproduct": "⨯", "xs": "×ˢ", "coprod": "⨿", "sigmaobj": "∐", "xf": "×ᶠ", "exf": "∃ᶠ", "c[": "⦃", "c]": "⦄", "Yot": "Ϳ", "goal": "⊢", "Vdash": "⊩", "Vert": "‖", "Vvdash": "⊪", "tiny": "⧾", "miny": "⧿"}