---
name: Bug report
about: Create a bug report
title: ''
labels: bug
assignees: ''

---

### Description

[Clear and concise description of the issue]

### Context

[Broader context that the issue occured in. If there was any prior discussion on [the Lean Zulip](https://leanprover.zulipchat.com), link it here as well.]

### Steps to Reproduce

1.
2.
3.

**Expected behavior:** [Clear and concise description of what you expect to happen]

**Actual behavior:** [Clear and concise description of what actually happens]

### Versions

[Version of vscode-lean4 (Hover over 'lean4' in the 'Extensions' menu)]  
[Output of `lean --version` in the folder that the issue occured in]  
[OS version]

### Additional Information

[Additional information, configuration or data that might be necessary to reproduce the issue]

### Impact

Add :+1: to [issues you consider important](https://github.com/leanprover/vscode-lean4/issues?q=is%3Aissue+is%3Aopen+sort%3Areactions-%2B1-desc). If others are impacted by this issue, please ask them to add :+1: to it.
