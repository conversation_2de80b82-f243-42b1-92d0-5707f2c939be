name: vscode-lean4 build

on:
  pull_request:
    branches:
    - '*'
  push:
    branches:
      - '*'
    tags:
      - '*'

jobs:
  build-and-test:
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: Linux
            os: ubuntu-latest
            artifact: build-Linux release
          # - name: macOS
          #   os: macos-latest
          #   artifact: build-macOS
          - name: Windows
            os: windows-latest
            artifact: build-Windows
    name: ${{ matrix.name }}
    runs-on: ${{ matrix.os }}

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '20'
        if: matrix.os == 'windows-latest'

      - name: Build
        run: |
          npm ci
          npm run build

      - name: Try publishing infoview-api
        if: ${{ startsWith(github.ref, 'refs/tags/v') && !endsWith(github.ref, '-pre') && matrix.os == 'ubuntu-latest' }}
        continue-on-error: true
        run: |
          npm publish --workspace=lean4-infoview-api --access=public

      - name: Try publishing infoview
        if: ${{ startsWith(github.ref, 'refs/tags/v') && !endsWith(github.ref, '-pre') && matrix.os == 'ubuntu-latest' }}
        continue-on-error: true
        run: |
          npm publish --workspace=lean4-infoview --access=public

      - name: Try publishing unicode-input
        if: ${{ startsWith(github.ref, 'refs/tags/v') && !endsWith(github.ref, '-pre') && matrix.os == 'ubuntu-latest' }}
        continue-on-error: true
        run: |
          npm publish --workspace=lean4-unicode-input --access=public

      - name: Try publishing unicode-input-component
        if: ${{ startsWith(github.ref, 'refs/tags/v') && !endsWith(github.ref, '-pre') && matrix.os == 'ubuntu-latest' }}
        continue-on-error: true
        run: |
          npm publish --workspace=lean4-unicode-input-component --access=public

      - name: Package
        run: npm run package --workspace=lean4
        if: ${{ !startsWith(github.ref, 'refs/tags/v') || !endsWith(github.ref, '-pre') }}

      - name: Package pre-release
        run: npm run packagePreRelease --workspace=lean4
        if: ${{ startsWith(github.ref, 'refs/tags/v') && endsWith(github.ref, '-pre') }}

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        if: matrix.os == 'ubuntu-latest'
        with:
          name: vscode-lean4
          path: 'vscode-lean4/lean4-*.vsix'

      - name: Publish packaged extension
        if: ${{ startsWith(github.ref, 'refs/tags/v') && !endsWith(github.ref, '-pre') && matrix.os == 'ubuntu-latest' }}
        run: |
          cd vscode-lean4
          npx @vscode/vsce publish -i lean4-*.vsix
          npx ovsx publish lean4-*.vsix
        env:
          OVSX_PAT: ${{ secrets.OVSX_PAT }}
          VSCE_PAT: ${{ secrets.VSCE_PAT }}

      - name: Publish packaged pre-release extension
        if: ${{ startsWith(github.ref, 'refs/tags/v') && endsWith(github.ref, '-pre') && matrix.os == 'ubuntu-latest' }}
        run: |
          cd vscode-lean4
          npx @vscode/vsce publish --pre-release -i lean4-*.vsix
          npx ovsx publish --pre-release lean4-*.vsix
        env:
          OVSX_PAT: ${{ secrets.OVSX_PAT }}
          VSCE_PAT: ${{ secrets.VSCE_PAT }}

      - name: Upload extension as release
        if: startsWith(github.ref, 'refs/tags/v') && matrix.os == 'ubuntu-latest'
        uses: softprops/action-gh-release@v1
        with:
          files: 'vscode-lean4/lean4-*.vsix'
          fail_on_unmatched_files: true
          prerelease: ${{ endsWith(github.ref, '-pre') }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Lint
        run: npm run lint

      - name: Install Brew Packages
        run: |
          brew install ccache tree zstd coreutils
        if: matrix.os == 'macos-latest'

      - name: Set path to elan on Linux or macOS
        if: matrix.os == 'ubuntu-latest' || matrix.os == 'macos-latest'
        run: |
          echo "$HOME/.elan/bin" >> $GITHUB_PATH

      - name: Set path to elan on Windows
        shell: pwsh
        if: matrix.os == 'windows-latest'
        run: |
          echo "$HOME\.elan\bin" >> $env:GITHUB_PATH

      - name: Run tests
        uses: GabrielBB/xvfb-action@v1.0
        with:
          run: npm run test

    env:
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
